import React, { useEffect, useRef } from 'react';
import styles from './AnimatedBackground.module.css';

interface SharedVertex {
  id: string;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  speed: number;
  baseX: number;
  baseY: number;
}

interface PolygonConfig {
  id: string;
  vertexIds: string[];
  color: string;
  opacity: number;
}

interface AnimatedBackgroundProps {
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ className }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const animationRef = useRef<number>();
  const verticesRef = useRef<Map<string, SharedVertex>>(new Map());

  // Define shared vertices for interconnected polygons
  const createSharedVertices = (): Map<string, SharedVertex> => {
    const vertices = new Map<string, SharedVertex>();

    // Create a grid-like network of vertices that will be shared between polygons
    const gridVertices = [
      // Top row
      { id: 'v1', x: 0, y: 0 },
      { id: 'v2', x: 400, y: 50 },
      { id: 'v3', x: 800, y: 0 },
      { id: 'v4', x: 1200, y: 80 },
      { id: 'v5', x: 1600, y: 0 },
      { id: 'v6', x: 1920, y: 100 },

      // Second row
      { id: 'v7', x: 0, y: 300 },
      { id: 'v8', x: 350, y: 250 },
      { id: 'v9', x: 700, y: 300 },
      { id: 'v10', x: 1050, y: 280 },
      { id: 'v11', x: 1400, y: 320 },
      { id: 'v12', x: 1920, y: 350 },

      // Third row
      { id: 'v13', x: 0, y: 600 },
      { id: 'v14', x: 300, y: 550 },
      { id: 'v15', x: 650, y: 600 },
      { id: 'v16', x: 1000, y: 580 },
      { id: 'v17', x: 1350, y: 620 },
      { id: 'v18', x: 1920, y: 650 },

      // Fourth row
      { id: 'v19', x: 0, y: 900 },
      { id: 'v20', x: 400, y: 850 },
      { id: 'v21', x: 800, y: 900 },
      { id: 'v22', x: 1200, y: 880 },
      { id: 'v23', x: 1600, y: 920 },
      { id: 'v24', x: 1920, y: 950 },

      // Bottom row
      { id: 'v25', x: 0, y: 1080 },
      { id: 'v26', x: 480, y: 1080 },
      { id: 'v27', x: 960, y: 1080 },
      { id: 'v28', x: 1440, y: 1080 },
      { id: 'v29', x: 1920, y: 1080 },
    ];

    gridVertices.forEach(vertex => {
      vertices.set(vertex.id, {
        id: vertex.id,
        x: vertex.x,
        y: vertex.y,
        baseX: vertex.x,
        baseY: vertex.y,
        targetX: vertex.x + (Math.random() - 0.5) * 120,
        targetY: vertex.y + (Math.random() - 0.5) * 120,
        speed: 0.03 + Math.random() * 0.004
      });
    });

    return vertices;
  };

  // Define interconnected polygons using shared vertices
  const polygonConfigs: PolygonConfig[] = [
    // Top layer polygons
    { id: 'poly1', vertexIds: ['v1', 'v2', 'v8', 'v7'], color: 'url(#oceanGradient1)', opacity: 0.15 },
    { id: 'poly2', vertexIds: ['v2', 'v3', 'v9', 'v8'], color: 'url(#oceanGradient2)', opacity: 0.12 },
    { id: 'poly3', vertexIds: ['v3', 'v4', 'v10', 'v9'], color: 'url(#oceanGradient1)', opacity: 0.13 },
    { id: 'poly4', vertexIds: ['v4', 'v5', 'v11', 'v10'], color: 'url(#oceanGradient3)', opacity: 0.12 },
    { id: 'poly5', vertexIds: ['v5', 'v6', 'v12', 'v11'], color: 'url(#oceanGradient2)', opacity: 0.1 },

    // Second layer polygons
    { id: 'poly6', vertexIds: ['v7', 'v8', 'v14', 'v13'], color: 'url(#oceanGradient2)', opacity: 0.13 },
    { id: 'poly7', vertexIds: ['v8', 'v9', 'v15', 'v14'], color: 'url(#oceanGradient1)', opacity: 0.15 },
    { id: 'poly8', vertexIds: ['v9', 'v10', 'v16', 'v15'], color: 'url(#oceanGradient3)', opacity: 0.12 },
    { id: 'poly9', vertexIds: ['v10', 'v11', 'v17', 'v16'], color: 'url(#oceanGradient2)', opacity: 0.13 },
    { id: 'poly10', vertexIds: ['v11', 'v12', 'v18', 'v17'], color: 'url(#oceanGradient1)', opacity: 0.1 },

    // Third layer polygons
    { id: 'poly11', vertexIds: ['v13', 'v14', 'v20', 'v19'], color: 'url(#oceanGradient1)', opacity: 0.15 },
    { id: 'poly12', vertexIds: ['v14', 'v15', 'v21', 'v20'], color: 'url(#oceanGradient3)', opacity: 0.13 },
    { id: 'poly13', vertexIds: ['v15', 'v16', 'v22', 'v21'], color: 'url(#oceanGradient2)', opacity: 0.12 },
    { id: 'poly14', vertexIds: ['v16', 'v17', 'v23', 'v22'], color: 'url(#oceanGradient1)', opacity: 0.13 },
    { id: 'poly15', vertexIds: ['v17', 'v18', 'v24', 'v23'], color: 'url(#oceanGradient3)', opacity: 0.1 },

    // Bottom layer polygons
    { id: 'poly16', vertexIds: ['v19', 'v20', 'v26', 'v25'], color: 'url(#oceanGradient2)', opacity: 0.15 },
    { id: 'poly17', vertexIds: ['v20', 'v21', 'v27', 'v26'], color: 'url(#oceanGradient1)', opacity: 0.13 },
    { id: 'poly18', vertexIds: ['v21', 'v22', 'v28', 'v27'], color: 'url(#oceanGradient3)', opacity: 0.12 },
    { id: 'poly19', vertexIds: ['v22', 'v23', 'v29', 'v28'], color: 'url(#oceanGradient2)', opacity: 0.13 },

    // Additional triangular polygons for more complexity
    { id: 'tri1', vertexIds: ['v2', 'v4', 'v9'], color: 'url(#oceanGradient1)', opacity: 0.08 },
    { id: 'tri2', vertexIds: ['v8', 'v10', 'v15'], color: 'url(#oceanGradient2)', opacity: 0.1 },
    { id: 'tri3', vertexIds: ['v14', 'v16', 'v21'], color: 'url(#oceanGradient3)', opacity: 0.08 },
    { id: 'tri4', vertexIds: ['v20', 'v22', 'v27'], color: 'url(#oceanGradient1)', opacity: 0.1 },
  ];

  useEffect(() => {
    // Initialize shared vertices
    verticesRef.current = createSharedVertices();

    const animate = () => {
      // Animate each shared vertex
      verticesRef.current.forEach(vertex => {
        // Move towards target
        vertex.x += (vertex.targetX - vertex.x) * vertex.speed;
        vertex.y += (vertex.targetY - vertex.y) * vertex.speed;

        // If close to target, set new random target around base position
        const distance = Math.sqrt(
          Math.pow(vertex.targetX - vertex.x, 2) + Math.pow(vertex.targetY - vertex.y, 2)
        );

        if (distance < 8) {
          // Create new target within a larger range from base position for more activity
          const maxOffset = 120;
          vertex.targetX = vertex.baseX + (Math.random() - 0.5) * maxOffset;
          vertex.targetY = vertex.baseY + (Math.random() - 0.5) * maxOffset;

          // Keep points within bounds
          vertex.targetX = Math.max(0, Math.min(1920, vertex.targetX));
          vertex.targetY = Math.max(0, Math.min(1080, vertex.targetY));
        }
      });

      // Update SVG polygons using shared vertices
      if (svgRef.current) {
        const polygons = svgRef.current.querySelectorAll('polygon[data-animated="true"]');
        polygons.forEach((polygon, polyIndex) => {
          const config = polygonConfigs[polyIndex];
          if (config) {
            const points = config.vertexIds.map(vertexId => {
              const vertex = verticesRef.current.get(vertexId);
              return vertex ? `${vertex.x},${vertex.y}` : '0,0';
            });

            polygon.setAttribute('points', points.join(' '));
          }
        });
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <svg
      ref={svgRef}
      className={`${styles.animatedBackground} ${className || ''}`}
      width="1920"
      height="1080"
      viewBox="0 0 1920 1080"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid slice"
    >
      <defs>
        <linearGradient id="oceanGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{stopColor: 'rgb(0, 57, 82)', stopOpacity: 1}} />
          <stop offset="50%" style={{stopColor: 'rgb(7, 110, 136)', stopOpacity: 0.8}} />
          <stop offset="100%" style={{stopColor: 'rgb(10, 133, 163)', stopOpacity: 0.6}} />
        </linearGradient>
        <linearGradient id="oceanGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style={{stopColor: 'rgb(10, 133, 163)', stopOpacity: 0.7}} />
          <stop offset="50%" style={{stopColor: 'rgb(0, 179, 164)', stopOpacity: 0.5}} />
          <stop offset="100%" style={{stopColor: 'rgb(0, 57, 82)', stopOpacity: 1}} />
        </linearGradient>
        <linearGradient id="oceanGradient3" x1="50%" y1="0%" x2="50%" y2="100%">
          <stop offset="0%" style={{stopColor: 'rgb(7, 110, 136)', stopOpacity: 0.6}} />
          <stop offset="50%" style={{stopColor: 'rgb(0, 179, 164)', stopOpacity: 0.4}} />
          <stop offset="100%" style={{stopColor: 'rgb(0, 57, 82)', stopOpacity: 1}} />
        </linearGradient>

        {/* Add subtle glow effects */}
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      {/* Background base with subtle gradient */}
      <rect width="1920" height="1080" fill="url(#oceanGradient1)" opacity="0.8"/>

      {/* Interconnected animated polygons */}
      {polygonConfigs.map((config) => {
        // Get initial points for the polygon
        const initialPoints = config.vertexIds.map(vertexId => {
          const vertices = createSharedVertices();
          const vertex = vertices.get(vertexId);
          return vertex ? `${vertex.baseX},${vertex.baseY}` : '0,0';
        });

        return (
          <polygon
            key={config.id}
            data-animated="true"
            points={initialPoints.join(' ')}
            fill={config.color}
            opacity={config.opacity}
            filter="url(#glow)"
            style={{
              transition: 'opacity 0.3s ease-in-out'
            }}
          />
        );
      })}

      {/* Subtle edge highlighting for interconnected effect */}
      <g opacity="0.15" stroke="rgba(0, 179, 164, 0.4)" strokeWidth="0.5" fill="none">
        {/* Grid-like connection lines that follow the vertex structure */}
        {/* Horizontal connections */}
        <line x1="0" y1="0" x2="400" y2="50" />
        <line x1="400" y1="50" x2="800" y2="0" />
        <line x1="800" y1="0" x2="1200" y2="80" />
        <line x1="1200" y1="80" x2="1600" y2="0" />
        <line x1="1600" y1="0" x2="1920" y2="100" />

        <line x1="0" y1="300" x2="350" y2="250" />
        <line x1="350" y1="250" x2="700" y2="300" />
        <line x1="700" y1="300" x2="1050" y2="280" />
        <line x1="1050" y1="280" x2="1400" y2="320" />
        <line x1="1400" y1="320" x2="1920" y2="350" />

        {/* Vertical connections */}
        <line x1="0" y1="0" x2="0" y2="300" />
        <line x1="400" y1="50" x2="350" y2="250" />
        <line x1="800" y1="0" x2="700" y2="300" />
        <line x1="1200" y1="80" x2="1050" y2="280" />
        <line x1="1600" y1="0" x2="1400" y2="320" />
        <line x1="1920" y1="100" x2="1920" y2="350" />

        {/* Diagonal connections for more complexity */}
        <line x1="400" y1="50" x2="700" y2="300" />
        <line x1="800" y1="0" x2="1050" y2="280" />
        <line x1="350" y1="250" x2="650" y2="600" />
        <line x1="1050" y1="280" x2="1350" y2="620" />
      </g>

      {/* Animated connection points */}
      <g opacity="0.3">
        {/* Small circles at key intersection points */}
        <circle cx="400" cy="50" r="2" fill="rgba(0, 179, 164, 0.6)">
          <animate attributeName="r" values="2;4;2" dur="4s" repeatCount="indefinite" />
        </circle>
        <circle cx="800" cy="0" r="2" fill="rgba(0, 179, 164, 0.6)">
          <animate attributeName="r" values="2;4;2" dur="5s" repeatCount="indefinite" />
        </circle>
        <circle cx="1200" cy="80" r="2" fill="rgba(0, 179, 164, 0.6)">
          <animate attributeName="r" values="2;4;2" dur="6s" repeatCount="indefinite" />
        </circle>
        <circle cx="700" cy="300" r="2" fill="rgba(0, 179, 164, 0.6)">
          <animate attributeName="r" values="2;4;2" dur="4.5s" repeatCount="indefinite" />
        </circle>
        <circle cx="1050" cy="280" r="2" fill="rgba(0, 179, 164, 0.6)">
          <animate attributeName="r" values="2;4;2" dur="5.5s" repeatCount="indefinite" />
        </circle>
      </g>
    </svg>
  );
};

export default AnimatedBackground;
