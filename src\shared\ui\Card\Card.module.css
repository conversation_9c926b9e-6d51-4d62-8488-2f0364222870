.card {
  position: relative;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

/* Card variants */
.variant-default {
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.variant-elevated {
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

.variant-outlined {
  background-color: transparent;
  border: 1px solid var(--border-color);
}

.variant-subtle {
  background-color: var(--bg-card);
  border: none;
}

/* Card accents */
.accent-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  z-index: 1;
}

.accent-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-secondary);
  z-index: 1;
}

.accent-danger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--neon-pink);
  z-index: 1;
}

.accent-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--neon-green);
  z-index: 1;
}

.accent-warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--neon-yellow);
  z-index: 1;
}

.accent-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--neon-blue);
  z-index: 1;
}

/* Clickable card */
.clickable {
  cursor: pointer;
}

.clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.clickable:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* Card header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
}

.headerContent {
  flex: 1;
}

.title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-on-card);
  text-transform: uppercase;
}

.subtitle {
  margin-top: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--text-on-card);
}

.extra {
  margin-left: var(--space-md);
}

/* Card body */
.body {
  padding: var(--space-md);
}

/* Card footer */
.footer {
  display: flex;
  padding: var(--space-md);
  border-top: 1px solid var(--border-color);
}

.align-left {
  justify-content: flex-start;
}

.align-center {
  justify-content: center;
}

.align-right {
  justify-content: flex-end;
}

.align-space-between {
  justify-content: space-between;
}

/* Cyberpunk-specific styling */
.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
    to bottom right,
    rgba(0, 240, 255, 0.05) 0%,
    transparent 40%,
    transparent 60%,
    rgba(0, 240, 255, 0.05) 100%
  );
  pointer-events: none;
}

/* Animation for elevated card */
.variant-elevated::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.2);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
  z-index: -1;
}

.variant-elevated:hover::before {
  opacity: 1;
}
