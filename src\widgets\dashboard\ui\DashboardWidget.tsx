import React from 'react';
import { dashboardStats, activeMissions, recentEvents } from '../../../shared/data/mock';
import styles from './DashboardWidget.module.css';

interface DashboardWidgetProps {
  tabId: string;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({ tabId }) => {
  return (
    <div className={styles.dashboardWidget}>
      <div className={styles.dashboardHeader}>
        <h1 className={styles.dashboardTitle}>Operations Dashboard</h1>
        <span className={styles.welcomeMessage}>Welcome back, Operator</span>
      </div>
      
      <div className={styles.dashboardContent}>
        {/* Resource summary section */}
        <section className={styles.resourceSummary}>
          <h2 className={styles.sectionTitle}>Resources</h2>
          <div className={styles.resourceGrid}>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.credits.toLocaleString()}</span>
              <span className={styles.resourceName}>Credits</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.activeMissions}</span>
              <span className={styles.resourceName}>Active Missions</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.availableSpecialists}</span>
              <span className={styles.resourceName}>Available Team</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.reputation}</span>
              <span className={styles.resourceName}>Reputation</span>
            </div>
          </div>
        </section>

        {/* Active missions section */}
        <section className={styles.activeMissions}>
          <h2 className={styles.sectionTitle}>Active Missions</h2>
          <div className={styles.missionsList}>
            {activeMissions.map(mission => (
              <div key={mission.id} className={styles.missionCard}>
                <div className={styles.missionHeader}>
                  <h3 className={styles.missionTitle}>{mission.title}</h3>
                  <span className={`${styles.difficultyBadge} ${styles[mission.difficulty]}`}>
                    {mission.difficulty}
                  </span>
                </div>
                <div className={styles.missionDetails}>
                  <span className={styles.missionFaction}>{mission.faction}</span>
                  <div className={styles.progressBar}>
                    <div 
                      className={styles.progressFill} 
                      style={{ width: `${mission.progress}%` }}
                    />
                  </div>
                  <span className={styles.progressText}>{mission.progress}%</span>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Recent events section */}
        <section className={styles.recentEvents}>
          <h2 className={styles.sectionTitle}>Recent Events</h2>
          <div className={styles.eventsList}>
            {recentEvents.map(event => (
              <div key={event.id} className={`${styles.eventItem} ${styles[event.type]}`}>
                <div className={styles.eventContent}>
                  <span className={styles.eventTitle}>{event.title}</span>
                  <span className={styles.eventTime}>{event.time}</span>
                </div>
                {event.description && (
                  <span className={styles.eventDescription}>{event.description}</span>
                )}
              </div>
            ))}
          </div>
        </section>

        {/* Faction standings section */}
        <section className={styles.factionStandings}>
          <h2 className={styles.sectionTitle}>Faction Standings</h2>
          <div className={styles.standingsList}>
            <div className={styles.standingItem}>
              <span className={styles.factionName}>Crime Syndicate</span>
              <div className={styles.standingBar}>
                <div 
                  className={`${styles.standingFill} ${styles.positive}`} 
                  style={{ width: '65%' }}
                ></div>
              </div>
              <span className={styles.standingValue}>+65</span>
            </div>
            <div className={styles.standingItem}>
              <span className={styles.factionName}>State-Sponsored</span>
              <div className={styles.standingBar}>
                <div 
                  className={`${styles.standingFill} ${styles.positive}`} 
                  style={{ width: '30%' }}
                ></div>
              </div>
              <span className={styles.standingValue}>+30</span>
            </div>
            <div className={styles.standingItem}>
              <span className={styles.factionName}>Hacktivists</span>
              <div className={styles.standingBar}>
                <div 
                  className={`${styles.standingFill} ${styles.negative}`} 
                  style={{ width: '20%' }}
                ></div>
              </div>
              <span className={styles.standingValue}>-20</span>
            </div>
            <div className={styles.standingItem}>
              <span className={styles.factionName}>Security Operations</span>
              <div className={styles.standingBar}>
                <div 
                  className={`${styles.standingFill} ${styles.positive}`} 
                  style={{ width: '10%' }}
                ></div>
              </div>
              <span className={styles.standingValue}>+10</span>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default DashboardWidget;
