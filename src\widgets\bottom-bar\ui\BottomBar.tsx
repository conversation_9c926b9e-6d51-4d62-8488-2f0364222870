import React from 'react';
import styles from './BottomBar.module.css';
import { useTabsStore } from '../../../features/tabs/model/store';
import {
  IconLayoutDashboard,
  IconTerminal,
  IconTarget,
  IconShoppingCart,
  IconUsers,
  IconSearch
} from '@tabler/icons-react';

// App definitions
interface AppDefinition {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  type: string;
}

const apps: AppDefinition[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: <IconLayoutDashboard size={20} stroke={1.5} />,
    description: 'Main overview of your operation',
    type: 'dashboard'
  },
  {
    id: 'terminal',
    name: 'Terminal',
    icon: <IconTerminal size={20} stroke={1.5} />,
    description: 'Command line interface',
    type: 'terminal'
  },
  {
    id: 'missions',
    name: 'Missions',
    icon: <IconTarget size={20} stroke={1.5} />,
    description: 'Manage your operations',
    type: 'mission'
  },
  {
    id: 'market',
    name: 'Market',
    icon: <IconShoppingCart size={20} stroke={1.5} />,
    description: 'Buy and sell resources',
    type: 'market'
  },
  {
    id: 'team',
    name: 'Team',
    icon: <IconUsers size={20} stroke={1.5} />,
    description: 'Manage your specialists',
    type: 'team'
  },
  {
    id: 'intel',
    name: 'Intel',
    icon: <IconSearch size={20} stroke={1.5} />,
    description: 'Analyze gathered intelligence',
    type: 'intel'
  }
];

const BottomBar: React.FC = () => {
  const { tabs, toggleTab } = useTabsStore();

  // Get active tab IDs
  const activeTabIds = tabs.map(tab => tab.id);

  const handleAppClick = (app: AppDefinition) => {
    // Use toggle functionality - this will add if not present, remove if present
    toggleTab({
      id: app.id,
      title: app.name,
      type: app.type,
      closable: app.id !== 'dashboard', // Dashboard can't be closed
      icon: app.icon
    });
  };
  
  return (
    <div className={styles.bottomBar}>
      <div className={styles.appLauncher}>
        {apps.map(app => {
          const isActive = activeTabIds.includes(app.id);
          return (
            <button
              key={app.id}
              className={`${styles.appButton} ${isActive ? styles.active : ''}`}
              onClick={() => handleAppClick(app)}
              title={`${app.name}: ${app.description}${isActive ? ' (Click to close)' : ' (Click to open)'}`}
            >
              <div className={`${styles.iconWrapper} ${isActive ? styles.active : ''}`}>
                {app.icon}
              </div>
              <span className={styles.appName}>{app.name}</span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomBar;