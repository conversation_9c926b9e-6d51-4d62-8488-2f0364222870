/**
 * Mock training program data for development and testing
 */

import { SkillType } from '@/core/specialist/types';

export interface TrainingProgram {
  id: string;
  name: string;
  description: string;
  type: 'technical' | 'field' | 'intelligence' | 'physical';
  duration: number; // in days
  cost: number;
  skillImprovements: Array<{
    skill: SkillType;
    bonus: number;
  }>;
  requiredLevel?: number;
  prerequisites?: string[];
}

export const mockTrainingPrograms: TrainingProgram[] = [
  {
    id: 'training-1',
    name: 'Network Infiltration Basics',
    description: 'Learn the fundamentals of network penetration and access techniques. Covers basic port scanning, vulnerability assessment, and exploitation methods.',
    type: 'technical',
    duration: 5,
    cost: 2500,
    skillImprovements: [
      { skill: 'network_infiltration', bonus: 10 },
      { skill: 'hacking', bonus: 5 }
    ]
  },
  {
    id: 'training-2',
    name: 'Advanced Social Engineering',
    description: 'Master the art of human manipulation and deception techniques. Learn to exploit psychological vulnerabilities and build trust with targets.',
    type: 'field',
    duration: 7,
    cost: 3800,
    skillImprovements: [
      { skill: 'social_engineering', bonus: 15 },
      { skill: 'persuasion', bonus: 10 },
      { skill: 'deception', bonus: 8 }
    ],
    requiredLevel: 2
  },
  {
    id: 'training-3',
    name: 'Cryptography and Encryption',
    description: 'Deep dive into modern cryptographic systems, encryption algorithms, and code-breaking techniques.',
    type: 'technical',
    duration: 10,
    cost: 4500,
    skillImprovements: [
      { skill: 'cryptography', bonus: 20 },
      { skill: 'hacking', bonus: 8 }
    ],
    requiredLevel: 3
  },
  {
    id: 'training-4',
    name: 'Malware Development Workshop',
    description: 'Learn to create custom malware, trojans, and exploits. Covers advanced programming techniques and evasion methods.',
    type: 'technical',
    duration: 14,
    cost: 6000,
    skillImprovements: [
      { skill: 'malware_development', bonus: 25 },
      { skill: 'exploitation', bonus: 15 },
      { skill: 'hacking', bonus: 10 }
    ],
    requiredLevel: 4,
    prerequisites: ['training-1']
  },
  {
    id: 'training-5',
    name: 'OSINT Mastery',
    description: 'Advanced open-source intelligence gathering techniques. Learn to extract valuable information from public sources.',
    type: 'intelligence',
    duration: 6,
    cost: 3200,
    skillImprovements: [
      { skill: 'osint', bonus: 18 },
      { skill: 'intel_analysis', bonus: 12 },
      { skill: 'threat_hunting', bonus: 8 }
    ]
  },
  {
    id: 'training-6',
    name: 'Physical Security Bypass',
    description: 'Learn to defeat physical security measures including locks, alarms, and surveillance systems.',
    type: 'physical',
    duration: 8,
    cost: 4200,
    skillImprovements: [
      { skill: 'physical_security', bonus: 20 },
      { skill: 'surveillance', bonus: 10 },
      { skill: 'counter_surveillance', bonus: 8 }
    ],
    requiredLevel: 2
  },
  {
    id: 'training-7',
    name: 'Advanced Threat Hunting',
    description: 'Proactive threat detection and analysis techniques. Learn to identify and track sophisticated adversaries.',
    type: 'intelligence',
    duration: 12,
    cost: 5500,
    skillImprovements: [
      { skill: 'threat_hunting', bonus: 22 },
      { skill: 'intel_analysis', bonus: 15 },
      { skill: 'osint', bonus: 10 }
    ],
    requiredLevel: 3,
    prerequisites: ['training-5']
  },
  {
    id: 'training-8',
    name: 'Elite Exploitation Techniques',
    description: 'Master-level exploitation methods and zero-day development. Reserved for the most skilled specialists.',
    type: 'technical',
    duration: 21,
    cost: 10000,
    skillImprovements: [
      { skill: 'exploitation', bonus: 30 },
      { skill: 'malware_development', bonus: 20 },
      { skill: 'hacking', bonus: 15 }
    ],
    requiredLevel: 5,
    prerequisites: ['training-4', 'training-3']
  }
];

export const mockBasicTrainingPrograms = mockTrainingPrograms.filter(p => !p.requiredLevel || p.requiredLevel <= 2);
export const mockAdvancedTrainingPrograms = mockTrainingPrograms.filter(p => p.requiredLevel && p.requiredLevel > 2);
