/**
 * API types for backend communication
 * These types will be shared between frontend and backend
 */

// Common API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication
export interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresAt: string;
  tokenType: 'Bearer';
}

export interface UserSession {
  userId: string;
  username: string;
  level: number;
  permissions: string[];
  sessionId: string;
  expiresAt: string;
}

// API Error codes
export enum ApiErrorCode {
  // Authentication
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // Validation
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  
  // Business Logic
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  
  // System
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

// Request/Response DTOs for each service

// User Service DTOs
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  preferences?: any;
}

export interface UserResponse {
  id: string;
  username: string;
  email: string;
  level: number;
  experience: number;
  credits: number;
  reputation: number;
  joinDate: string;
  lastActive: string;
}

// Mission Service DTOs
export interface StartMissionRequest {
  missionId: string;
  specialistIds: string[];
  equipment?: string[];
}

export interface MissionResponse {
  id: string;
  title: string;
  description: string;
  status: string;
  difficulty: string;
  progress: number;
  startedAt?: string;
  estimatedCompletion?: string;
}

// Market Service DTOs
export interface PurchaseItemRequest {
  itemId: string;
  quantity: number;
}

export interface MarketItemResponse {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  rarity: string;
  available: boolean;
  stock?: number;
}

// Specialist Service DTOs
export interface HireSpecialistRequest {
  specialistId: string;
}

export interface SpecialistResponse {
  id: string;
  name: string;
  specialization: string;
  level: number;
  skills: Record<string, number>;
  status: string;
  cost: number;
}

// Security Service DTOs
export interface SecurityThreatResponse {
  id: string;
  name: string;
  description: string;
  severity: string;
  type: string;
  status: string;
  detectedAt: string;
}

export interface ActivateCountermeasureRequest {
  countermeasureId: string;
  duration?: number;
}

// Notification Service DTOs
export interface CreateNotificationRequest {
  title: string;
  message: string;
  type: string;
  priority?: string;
  category?: string;
  expiresIn?: number;
}

export interface NotificationResponse {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  category: string;
  timestamp: string;
  read: boolean;
  dismissed: boolean;
}

// WebSocket message types for real-time communication
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  id: string;
}

export interface EventMessage extends WebSocketMessage {
  type: 'event';
  payload: {
    eventType: string;
    eventData: any;
  };
}

export interface NotificationMessage extends WebSocketMessage {
  type: 'notification';
  payload: NotificationResponse;
}

export interface SystemMessage extends WebSocketMessage {
  type: 'system';
  payload: {
    message: string;
    level: 'info' | 'warning' | 'error';
  };
}
