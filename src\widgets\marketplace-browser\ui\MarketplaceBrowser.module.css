.marketplace-browser {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  background: var(--bg-hover);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--space-md);
}

.title {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-on-card);
  text-transform: uppercase;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.categories {
  width: 220px;
  padding: var(--space-md);
  background-color: rgba(26, 26, 26, 0.7);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.category {
  padding: var(--space-sm) var(--space-md);
  margin-bottom: var(--space-xs);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  font-weight: 500;
  border-left: 2px solid transparent;
}

.category:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-left-color: var(--neon-cyan);
}

.category.active {
  background-color: rgba(0, 240, 255, 0.1);
  border-left-color: var(--neon-cyan);
  color: var(--neon-cyan);
}

.items {
  flex: 1;
  padding: var(--space-md);
  overflow-y: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--space-xl);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: var(--space-md);
  opacity: 0.5;
}

.empty-text {
  font-size: var(--font-size-md);
  max-width: 300px;
}

.item-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: var(--space-md);
}

.item-card {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border-color: var(--neon-cyan);
}

.item-image {
  height: 120px;
  background-color: #333;
  background-size: cover;
  background-position: center;
  border-bottom: 1px solid var(--border-color);
}

.item-content {
  padding: var(--space-md);
}

.item-title {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-md);
  font-weight: 600;
}

.item-description {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-sm);
}

.item-price {
  font-weight: 600;
  color: var(--neon-cyan);
}

.item-rarity {
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  text-transform: uppercase;
}

.rarity-common {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
}

.rarity-uncommon {
  background-color: rgba(52, 199, 89, 0.2);
  color: #34C759;
}

.rarity-rare {
  background-color: rgba(0, 122, 255, 0.2);
  color: #007AFF;
}

.rarity-epic {
  background-color: rgba(175, 82, 222, 0.2);
  color: #AF52DE;
}

.rarity-legendary {
  background-color: rgba(255, 149, 0, 0.2);
  color: #FF9500;
}