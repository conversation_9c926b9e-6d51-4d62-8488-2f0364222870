/* Modern Tab Bar Styles */

.tabBar {
  display: flex;
  align-items: center;
  height: var(--tab-height);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  padding: 0 var(--space-md);
  overflow-x: auto;
  scrollbar-width: thin;
  position: relative;
}

/* Subtle accent line */
.tabBar::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: var(--space-md);
  right: var(--space-md);
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-500), transparent);
  opacity: 0.3;
}

.tab {
  display: flex;
  align-items: center;
  height: calc(var(--tab-height) - 4px);
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--text-on-card);
  background: var(--bg-hover);
  border: none;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  margin-right: var(--space-xs);
  user-select: none;
  white-space: nowrap;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.tab:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.tabActive {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: none;
  border-bottom: 2px solid rgba(0, 179, 164, 0.8);
}

.tabActive::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(0, 179, 164, 0.8), rgba(10, 133, 163, 0.8));
}

.tabIcon {
  margin-right: var(--space-xs);
  font-size: 16px;
  opacity: 0.9;
}

.tabText {
  font-family: var(--font-main);
  font-weight: 500;
  opacity: 1;
}

.tabCloseButton {
  margin-left: var(--space-xs);
  opacity: 0.6;
  font-size: 14px;
  transition: opacity var(--transition-fast);
  padding: 2px;
  border-radius: var(--border-radius-sm);
}

.tabCloseButton:hover {
  opacity: 1;
  color: var(--color-error);
  background: rgba(239, 68, 68, 0.1);
}

.addTab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  margin-left: var(--space-md);
  font-size: 16px;
  color: var(--text-on-card);
  background: var(--bg-hover);
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.addTab:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}
