/**
 * Core domain interfaces for User/Player functionality
 */

import { UserProfile, Achievement, UserPreferences, UserResource, LevelRequirement } from './types';

export interface UserRepository {
  // Profile operations
  getUserProfile(userId: string): Promise<UserProfile | null>;
  updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void>;
  createUserProfile(profile: Omit<UserProfile, 'id'>): Promise<UserProfile>;
  
  // Resource operations
  getUserResources(userId: string): Promise<UserResource[]>;
  updateUserResource(userId: string, resource: UserResource): Promise<void>;
  
  // Achievement operations
  getUserAchievements(userId: string): Promise<Achievement[]>;
  unlockAchievement(userId: string, achievementId: string): Promise<void>;
  
  // Preferences operations
  getUserPreferences(userId: string): Promise<UserPreferences>;
  updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void>;
}

export interface IUserService {
  // Profile management
  getCurrentUser(): Promise<UserProfile | null>;
  updateProfile(updates: Partial<UserProfile>): Promise<void>;
  
  // Experience and leveling
  addExperience(amount: number): Promise<void>;
  checkLevelUp(): Promise<boolean>;
  getLevelRequirements(level: number): Promise<LevelRequirement | null>;
  
  // Resource management
  addCredits(amount: number): Promise<void>;
  spendCredits(amount: number): Promise<boolean>;
  addReputation(amount: number): Promise<void>;
  
  // Achievement system
  checkAchievements(): Promise<Achievement[]>;
  unlockAchievement(achievementId: string): Promise<void>;
  
  // Preferences
  getPreferences(): Promise<UserPreferences>;
  updatePreferences(preferences: Partial<UserPreferences>): Promise<void>;
  
  // Statistics
  updateStats(statUpdates: Partial<UserProfile['stats']>): Promise<void>;
  getPlayerRank(): Promise<number>;
}
