.dashboardWidget {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
}

.dashboardHeader {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(0, 179, 164, 0.2);
}

.dashboardTitle {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.welcomeMessage {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
}

.dashboardContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.sectionTitle {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* Resource Summary */
.resourceSummary {
  margin-bottom: var(--space-lg);
}

.resourceGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
}

.resourceCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-md);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
}

.resourceCard:hover {
  background: rgba(0, 179, 164, 0.1);
  transform: translateY(-2px);
}

.resourceValue {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: var(--space-xs);
}

.resourceName {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* Active Missions */
.activeMissions {
  margin-bottom: var(--space-lg);
}

.missionsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.missionCard {
  padding: var(--space-md);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
}

.missionCard:hover {
  background: rgba(0, 179, 164, 0.1);
  transform: translateY(-2px);
}

.missionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.missionTitle {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
}

.difficultyBadge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
}

.novice {
  background-color: rgba(52, 199, 89, 0.2);
  color: #34C759;
}

.professional {
  background-color: rgba(0, 122, 255, 0.2);
  color: #007AFF;
}

.expert {
  background-color: rgba(255, 149, 0, 0.2);
  color: #FF9500;
}

.elite, .legendary {
  background-color: rgba(255, 45, 85, 0.2);
  color: #FF2D55;
}

.missionDetails {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.missionFaction {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.progressBar {
  flex: 1;
  height: 6px;
  background-color: rgba(0, 179, 164, 0.2);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background-color: var(--accent-primary);
  border-radius: var(--border-radius-sm);
  transition: width var(--transition-normal);
}

.progressText {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  min-width: 40px;
  text-align: right;
}

/* Recent Events */
.recentEvents {
  margin-bottom: var(--space-lg);
}

.eventsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.eventItem {
  display: flex;
  align-items: flex-start;
  padding: var(--space-sm);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border-left: 3px solid transparent;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.eventItem:hover {
  background: rgba(0, 179, 164, 0.1);
}

.eventItem.info {
  border-left-color: #007AFF;
}

.eventItem.success {
  border-left-color: #34C759;
}

.eventItem.warning {
  border-left-color: #FF9500;
}

.eventItem.error {
  border-left-color: #FF2D55;
}

.eventContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.eventTitle {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.eventTime {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.eventDescription {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--space-xs);
}

/* Faction Standings */
.factionStandings {
  margin-bottom: var(--space-lg);
}

.standingsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.standingItem {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-sm);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.standingItem:hover {
  background: rgba(0, 179, 164, 0.1);
}

.factionName {
  flex: 0 0 150px;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.standingBar {
  flex: 1;
  height: 8px;
  background-color: rgba(0, 179, 164, 0.2);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  position: relative;
}

.standingFill {
  height: 100%;
  border-radius: var(--border-radius-sm);
  transition: width var(--transition-normal);
}

.standingFill.positive {
  background-color: #34C759;
}

.standingFill.negative {
  background-color: #FF3B30;
}

.standingValue {
  flex: 0 0 40px;
  text-align: right;
  font-weight: 500;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .resourceGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .missionDetails {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .progressBar {
    width: 100%;
  }
  
  .factionName {
    flex: 0 0 auto;
    margin-bottom: var(--space-xs);
  }
  
  .standingItem {
    flex-direction: column;
    align-items: flex-start;
  }
}
