<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="oceanGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgb(0, 57, 82);stop-opacity:1" />
      <stop offset="50%" style="stop-color:rgb(7, 110, 136);stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:rgb(10, 133, 163);stop-opacity:0.6" />
    </linearGradient>
    <linearGradient id="oceanGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgb(10, 133, 163);stop-opacity:0.7" />
      <stop offset="50%" style="stop-color:rgb(0, 179, 164);stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:rgb(0, 57, 82);stop-opacity:1" />
    </linearGradient>
    <linearGradient id="oceanGradient3" x1="50%" y1="0%" x2="50%" y2="100%">
      <stop offset="0%" style="stop-color:rgb(7, 110, 136);stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:rgb(0, 57, 82);stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background base -->
  <rect width="1920" height="1080" fill="rgb(0, 57, 82)"/>
  
  <!-- Large polygonal shapes -->
  <polygon points="0,0 600,0 400,300 0,200" fill="url(#oceanGradient1)" opacity="0.3"/>
  <polygon points="600,0 1200,0 1000,250 800,400 400,300" fill="url(#oceanGradient2)" opacity="0.25"/>
  <polygon points="1200,0 1920,0 1920,300 1400,200 1000,250" fill="url(#oceanGradient1)" opacity="0.2"/>
  
  <!-- Mid-section polygons -->
  <polygon points="0,200 400,300 200,600 0,500" fill="url(#oceanGradient3)" opacity="0.4"/>
  <polygon points="400,300 800,400 600,700 300,650 200,600" fill="url(#oceanGradient1)" opacity="0.3"/>
  <polygon points="800,400 1400,200 1600,500 1200,650 600,700" fill="url(#oceanGradient2)" opacity="0.25"/>
  <polygon points="1400,200 1920,300 1920,600 1600,500" fill="url(#oceanGradient3)" opacity="0.35"/>
  
  <!-- Bottom section -->
  <polygon points="0,500 200,600 100,900 0,800" fill="url(#oceanGradient2)" opacity="0.3"/>
  <polygon points="200,600 300,650 600,700 400,950 100,900" fill="url(#oceanGradient1)" opacity="0.25"/>
  <polygon points="600,700 1200,650 1000,950 700,1000 400,950" fill="url(#oceanGradient3)" opacity="0.4"/>
  <polygon points="1200,650 1600,500 1920,600 1920,900 1400,1000 1000,950" fill="url(#oceanGradient2)" opacity="0.3"/>
  
  <!-- Bottom edge -->
  <polygon points="0,800 100,900 0,1080" fill="url(#oceanGradient1)" opacity="0.2"/>
  <polygon points="100,900 400,950 300,1080 0,1080" fill="url(#oceanGradient3)" opacity="0.25"/>
  <polygon points="400,950 700,1000 600,1080 300,1080" fill="url(#oceanGradient2)" opacity="0.3"/>
  <polygon points="700,1000 1400,1000 1200,1080 600,1080" fill="url(#oceanGradient1)" opacity="0.2"/>
  <polygon points="1400,1000 1920,900 1920,1080 1200,1080" fill="url(#oceanGradient3)" opacity="0.35"/>
  
  <!-- Accent polygons for depth -->
  <polygon points="300,150 500,100 450,250 350,280" fill="rgba(0, 179, 164, 0.1)" opacity="0.6"/>
  <polygon points="1100,80 1350,120 1300,280 1050,240" fill="rgba(0, 179, 164, 0.15)" opacity="0.5"/>
  <polygon points="150,450 350,400 320,580 180,620" fill="rgba(10, 133, 163, 0.2)" opacity="0.4"/>
  <polygon points="1450,350 1650,320 1620,480 1480,520" fill="rgba(0, 179, 164, 0.1)" opacity="0.6"/>
  <polygon points="500,800 700,750 680,920 520,950" fill="rgba(7, 110, 136, 0.3)" opacity="0.3"/>
  <polygon points="1250,750 1450,720 1430,880 1270,910" fill="rgba(0, 179, 164, 0.12)" opacity="0.5"/>
</svg>
