// User module exports
import { UserService } from './UserService';
import { IUserService } from './interfaces';
import { MockUserRepository } from '../../features/user/repository';

// Create repository
const userRepository = new MockUserRepository();

// Create and export user service
export const userService: IUserService = new UserService(userRepository);

// Export types and interfaces
export * from './types';
export * from './interfaces';
export { UserService } from './UserService';
