// Core domain types for Marketplace functionality

export type MarketType = 'public' | 'gray' | 'dark';
export type ItemCategory = 'software' | 'hardware' | 'infrastructure' | 'data' | 'service';
export type ItemRarity = 'common' | 'uncommon' | 'rare' | 'legendary';
export type ItemLegality = 'legal' | 'gray' | 'illegal';
export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

export interface Requirement {
  type: 'level' | 'reputation' | 'skill' | 'item';
  value: string | number;
  description: string;
}

export interface Item {
  id: string;
  name: string;
  description: string;
  category: ItemCategory;
  rarity: ItemRarity;
  legality: ItemLegality;
  cost: number;
  discount?: number; // Percentage discount if on sale
  requirements?: Requirement[];
  capabilities?: string[];
  marketSource: MarketType;
  vendorId: string;
  available: boolean;
  stock?: number;
  tags?: string[];
}

export interface Vendor {
  id: string;
  name: string;
  description: string;
  reputation: number;
  marketType: MarketType;
  specialties: ItemCategory[];
  trustLevel: number;
  lastSeen: string;
}

export interface Transaction {
  id: string;
  itemId: string;
  vendorId: string;
  buyerId: string;
  quantity: number;
  totalCost: number;
  status: TransactionStatus;
  timestamp: string;
  completedAt?: string;
}

export interface MarketFilter {
  category?: ItemCategory;
  marketType?: MarketType;
  rarity?: ItemRarity;
  priceRange?: {
    min: number;
    max: number;
  };
  searchQuery?: string;
  availableOnly?: boolean;
}

export interface Market {
  id: string;
  name: string;
  description: string;
  type: MarketType;
  accessRequirements: Requirement[];
  vendors: string[]; // Vendor IDs
  isAccessible: boolean;
  lastRefresh: string;
}

export interface Requirement {
  type: 'reputation' | 'skill' | 'resource' | 'item' | 'level';
  id: string; // ID of faction, skill, resource, etc.
  value: number; // Required amount or level
}

export interface Vendor {
  id: string;
  name: string;
  description: string;
  marketType: MarketType;
  specialization?: ItemCategory[];
  reputation: number; // 0-100 scale
  items: string[]; // IDs of items sold
  relationshipLevel: number; // 0-5 scale
  accessRequirements?: Requirement[];
}

export interface Market {
  id: string;
  name: string;
  type: MarketType;
  accessRequirements: Requirement[];
  vendors: string[]; // Vendor IDs
  refreshInterval: number; // In game time units
  reputationImpact: Record<string, number>; // Impact on faction reputations
}
