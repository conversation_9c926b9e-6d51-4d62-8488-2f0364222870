/**
 * Service Registry - Centralized service registration
 * This configuration will be easily adaptable for backend
 */

import { container, singleton } from './ServiceContainer';
import { eventBus } from '../events/EventBus';

// Import all services
import { MarketService } from '../market/MarketService';
import { UserService } from '../user/UserService';
import { SecurityService } from '../security/SecurityService';
import { NotificationService } from '../notification/NotificationService';

// Import all repositories (these will be swapped for API clients on backend)
import { MockMarketRepository, MockVendorRepository, MockItemRepository, MockInventoryRepository } from '../../features/market/repository';
import { MockUserRepository } from '../../features/user/repository';
import { MockSecurityRepository } from '../../features/security/repository';
import { MockNotificationRepository } from '../../features/notification/repository';

// Service names (constants for type safety)
export const SERVICE_NAMES = {
  // Core Services
  MARKET_SERVICE: 'marketService',
  USER_SERVICE: 'userService',
  SECURITY_SERVICE: 'securityService',
  NOTIFICATION_SERVICE: 'notificationService',
  
  // Repositories
  MARKET_REPOSITORY: 'marketRepository',
  VENDOR_REPOSITORY: 'vendorRepository',
  ITEM_REPOSITORY: 'itemRepository',
  INVENTORY_REPOSITORY: 'inventoryRepository',
  USER_REPOSITORY: 'userRepository',
  SECURITY_REPOSITORY: 'securityRepository',
  NOTIFICATION_REPOSITORY: 'notificationRepository',
  
  // Infrastructure
  EVENT_BUS: 'eventBus',
} as const;

/**
 * Register all services with the container
 * This function will be called once at application startup
 */
export function registerServices(): void {
  // Register Event Bus
  container.register(SERVICE_NAMES.EVENT_BUS, singleton(() => eventBus));

  // Register Repositories
  container.register(SERVICE_NAMES.MARKET_REPOSITORY, singleton(() => new MockMarketRepository()));
  container.register(SERVICE_NAMES.VENDOR_REPOSITORY, singleton(() => new MockVendorRepository()));
  container.register(SERVICE_NAMES.ITEM_REPOSITORY, singleton(() => new MockItemRepository()));
  container.register(SERVICE_NAMES.INVENTORY_REPOSITORY, singleton(() => new MockInventoryRepository()));
  container.register(SERVICE_NAMES.USER_REPOSITORY, singleton(() => new MockUserRepository()));
  container.register(SERVICE_NAMES.SECURITY_REPOSITORY, singleton(() => new MockSecurityRepository()));
  
  container.register(SERVICE_NAMES.NOTIFICATION_REPOSITORY, singleton(() => new MockNotificationRepository()));

  // Register Core Services
  container.register(SERVICE_NAMES.MARKET_SERVICE, singleton((c) => new MarketService(
    c.get(SERVICE_NAMES.MARKET_REPOSITORY),
    c.get(SERVICE_NAMES.VENDOR_REPOSITORY),
    c.get(SERVICE_NAMES.ITEM_REPOSITORY),
    c.get(SERVICE_NAMES.INVENTORY_REPOSITORY)
  )));

  container.register(SERVICE_NAMES.USER_SERVICE, singleton((c) => new UserService(
    c.get(SERVICE_NAMES.USER_REPOSITORY)
  )));

  container.register(SERVICE_NAMES.SECURITY_SERVICE, singleton((c) => new SecurityService(
    c.get(SERVICE_NAMES.SECURITY_REPOSITORY)
  )));

  container.register(SERVICE_NAMES.NOTIFICATION_SERVICE, singleton((c) => new NotificationService(
    c.get(SERVICE_NAMES.NOTIFICATION_REPOSITORY)
  )));

  // Register event handlers for cross-service communication
  registerEventHandlers();
}

/**
 * Register event handlers for cross-service communication
 */
function registerEventHandlers(): void {
  const eventBus = container.get(SERVICE_NAMES.EVENT_BUS);
  const notificationService = container.get(SERVICE_NAMES.NOTIFICATION_SERVICE);
  const userService = container.get(SERVICE_NAMES.USER_SERVICE);

  // Mission completion events
  eventBus.subscribe('mission.completed', async (event: any) => {
    // Award experience and credits
    await userService.addExperience(event.data.rewards.experience);
    await userService.addCredits(event.data.rewards.credits);
    await userService.addReputation(event.data.rewards.reputation);

    // Create notification
    await notificationService.notifyMissionComplete(event.data.missionId, event.data.success);

    // Check for achievements
    await userService.checkAchievements();
  });

  // User level up events
  eventBus.subscribe('user.leveled_up', async (event: any) => {
    await notificationService.createNotification(
      'Level Up!',
      `Congratulations! You've reached level ${event.data.newLevel}!`,
      'success',
      {
        category: 'achievement',
        priority: 'high',
        metadata: event.data
      }
    );
  });

  // Achievement unlocked events
  eventBus.subscribe('user.achievement_unlocked', async (event: any) => {
    await notificationService.notifyAchievementUnlocked(event.data.achievementName);
  });

  // Specialist hired events
  eventBus.subscribe('specialist.hired', async (event: any) => {
    await notificationService.notifySpecialistHired(event.data.specialistName);
    
    // Update user stats
    await userService.updateStats({
      specialistsHired: (await userService.getCurrentUser())?.stats.specialistsHired + 1 || 1
    });
  });

  // Market purchase events
  eventBus.subscribe('market.item_purchased', async (event: any) => {
    await notificationService.createNotification(
      'Item Purchased',
      `You've successfully purchased ${event.data.itemName}!`,
      'success',
      {
        category: 'market',
        priority: 'medium',
        metadata: event.data
      }
    );
  });

  // Security threat events
  eventBus.subscribe('security.threat_detected', async (event: any) => {
    await notificationService.notifySecurityThreat(
      event.data.threatName,
      event.data.severity
    );
  });

  // Training completion events
  eventBus.subscribe('specialist.training_completed', async (event: any) => {
    await notificationService.createNotification(
      'Training Completed',
      `Specialist training has been completed successfully!`,
      'success',
      {
        category: 'team',
        priority: 'medium',
        metadata: event.data
      }
    );
  });
}

/**
 * Get a service from the container (type-safe helper)
 */
export function getService<T>(serviceName: string): T {
  return container.get<T>(serviceName);
}

/**
 * Service accessor helpers for common services
 */
export const getMarketService = () => getService(SERVICE_NAMES.MARKET_SERVICE);
export const getUserService = () => getService(SERVICE_NAMES.USER_SERVICE);
export const getSecurityService = () => getService(SERVICE_NAMES.SECURITY_SERVICE);
export const getNotificationService = () => getService(SERVICE_NAMES.NOTIFICATION_SERVICE);
export const getEventBus = () => getService(SERVICE_NAMES.EVENT_BUS);
