// Market module exports
import { MarketService } from './MarketService';
import { MarketService as IMarketService } from './interfaces';
import {
  MockMarketRepository,
  MockVendorRepository,
  MockItemRepository,
  MockInventoryRepository
} from '../../features/market/repository';

// Create repositories
const marketRepository = new MockMarketRepository();
const vendorRepository = new MockVendorRepository();
const itemRepository = new MockItemRepository();
const inventoryRepository = new MockInventoryRepository();

// Create and export market service
export const marketService: IMarketService = new MarketService(
  marketRepository,
  vendorRepository,
  itemRepository,
  inventoryRepository
);

// Export types and interfaces
export * from './types';
export * from './interfaces';
export { MarketService } from './MarketService';
