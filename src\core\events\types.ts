/**
 * Core domain events for cross-service communication
 * This event system will work identically on frontend and backend
 */

export interface DomainEvent {
  id: string;
  type: string;
  timestamp: string;
  source: string;
  data: Record<string, any>;
  version: number;
}

// Mission Events
export interface MissionStartedEvent extends DomainEvent {
  type: 'mission.started';
  data: {
    missionId: string;
    userId: string;
    specialistIds: string[];
    estimatedDuration: number;
  };
}

export interface MissionCompletedEvent extends DomainEvent {
  type: 'mission.completed';
  data: {
    missionId: string;
    userId: string;
    success: boolean;
    rewards: {
      credits: number;
      experience: number;
      reputation: number;
    };
    completedAt: string;
  };
}

export interface MissionFailedEvent extends DomainEvent {
  type: 'mission.failed';
  data: {
    missionId: string;
    userId: string;
    reason: string;
    penalties: {
      credits?: number;
      reputation?: number;
    };
  };
}

// User Events
export interface UserLeveledUpEvent extends DomainEvent {
  type: 'user.leveled_up';
  data: {
    userId: string;
    oldLevel: number;
    newLevel: number;
    unlockedFeatures: string[];
  };
}

export interface UserResourcesChangedEvent extends DomainEvent {
  type: 'user.resources_changed';
  data: {
    userId: string;
    changes: {
      credits?: number;
      experience?: number;
      reputation?: number;
    };
    newTotals: {
      credits: number;
      experience: number;
      reputation: number;
    };
  };
}

export interface AchievementUnlockedEvent extends DomainEvent {
  type: 'user.achievement_unlocked';
  data: {
    userId: string;
    achievementId: string;
    achievementName: string;
    rarity: string;
  };
}

// Specialist Events
export interface SpecialistHiredEvent extends DomainEvent {
  type: 'specialist.hired';
  data: {
    userId: string;
    specialistId: string;
    specialistName: string;
    cost: number;
  };
}

export interface SpecialistTrainingStartedEvent extends DomainEvent {
  type: 'specialist.training_started';
  data: {
    userId: string;
    specialistId: string;
    trainingProgramId: string;
    duration: number;
    cost: number;
  };
}

export interface SpecialistTrainingCompletedEvent extends DomainEvent {
  type: 'specialist.training_completed';
  data: {
    userId: string;
    specialistId: string;
    trainingProgramId: string;
    skillImprovements: Array<{
      skill: string;
      oldValue: number;
      newValue: number;
    }>;
  };
}

// Market Events
export interface ItemPurchasedEvent extends DomainEvent {
  type: 'market.item_purchased';
  data: {
    userId: string;
    itemId: string;
    itemName: string;
    cost: number;
    vendorId: string;
  };
}

export interface ItemSoldEvent extends DomainEvent {
  type: 'market.item_sold';
  data: {
    userId: string;
    itemId: string;
    itemName: string;
    sellPrice: number;
  };
}

// Security Events
export interface ThreatDetectedEvent extends DomainEvent {
  type: 'security.threat_detected';
  data: {
    userId: string;
    threatId: string;
    threatName: string;
    severity: string;
    threatType: string;
  };
}

export interface CountermeasureActivatedEvent extends DomainEvent {
  type: 'security.countermeasure_activated';
  data: {
    userId: string;
    countermeasureId: string;
    countermeasureName: string;
    cost: number;
    duration: number;
  };
}

// System Events
export interface SystemMaintenanceEvent extends DomainEvent {
  type: 'system.maintenance';
  data: {
    maintenanceType: string;
    startTime: string;
    estimatedDuration: number;
    affectedServices: string[];
  };
}

// Union type for all events
export type GameEvent = 
  | MissionStartedEvent
  | MissionCompletedEvent
  | MissionFailedEvent
  | UserLeveledUpEvent
  | UserResourcesChangedEvent
  | AchievementUnlockedEvent
  | SpecialistHiredEvent
  | SpecialistTrainingStartedEvent
  | SpecialistTrainingCompletedEvent
  | ItemPurchasedEvent
  | ItemSoldEvent
  | ThreatDetectedEvent
  | CountermeasureActivatedEvent
  | SystemMaintenanceEvent;

// Event handler type
export type EventHandler<T extends DomainEvent = DomainEvent> = (event: T) => Promise<void> | void;

// Event subscription
export interface EventSubscription {
  id: string;
  eventType: string;
  handler: EventHandler;
  once?: boolean;
}
