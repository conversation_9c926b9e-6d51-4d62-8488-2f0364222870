/**
 * Mock application definitions for the bottom bar
 */

import React from 'react';
import {
  IconLayoutDashboard,
  IconTerminal,
  IconTarget,
  IconShoppingCart,
  IconUsers,
  IconSearch,
  IconMessage,
  IconSettings,
  IconBook
} from '@tabler/icons-react';
import { TabType } from '@/shared/types/common';

export interface AppDefinition {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  type: TabType;
  category?: 'core' | 'tools' | 'communication' | 'settings';
  requiredLevel?: number;
}

export const mockApps: AppDefinition[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: React.createElement(IconLayoutDashboard, { size: 20, stroke: 1.5 }),
    description: 'Main overview of your operation',
    type: 'dashboard',
    category: 'core'
  },
  {
    id: 'terminal',
    name: 'Terminal',
    icon: React.createElement(IconTerminal, { size: 20, stroke: 1.5 }),
    description: 'Command line interface',
    type: 'terminal',
    category: 'tools'
  },
  {
    id: 'missions',
    name: 'Missions',
    icon: React.createElement(IconTarget, { size: 20, stroke: 1.5 }),
    description: 'Manage your operations',
    type: 'mission',
    category: 'core'
  },
  {
    id: 'market',
    name: 'Market',
    icon: React.createElement(IconShoppingCart, { size: 20, stroke: 1.5 }),
    description: 'Buy and sell resources',
    type: 'market',
    category: 'tools'
  },
  {
    id: 'team',
    name: 'Team',
    icon: React.createElement(IconUsers, { size: 20, stroke: 1.5 }),
    description: 'Manage your specialists',
    type: 'team',
    category: 'core'
  },
  {
    id: 'intel',
    name: 'Intel',
    icon: React.createElement(IconSearch, { size: 20, stroke: 1.5 }),
    description: 'Analyze gathered intelligence',
    type: 'intel',
    category: 'tools'
  },
  {
    id: 'messaging',
    name: 'Messages',
    icon: React.createElement(IconMessage, { size: 20, stroke: 1.5 }),
    description: 'Secure communications',
    type: 'messaging',
    category: 'communication'
  },
  {
    id: 'training',
    name: 'Training',
    icon: React.createElement(IconBook, { size: 20, stroke: 1.5 }),
    description: 'Specialist training programs',
    type: 'team', // Uses team type for now
    category: 'core'
  },
  {
    id: 'settings',
    name: 'Settings',
    icon: React.createElement(IconSettings, { size: 20, stroke: 1.5 }),
    description: 'System configuration',
    type: 'settings',
    category: 'settings'
  }
];

export const mockCoreApps = mockApps.filter(app => app.category === 'core');
export const mockToolApps = mockApps.filter(app => app.category === 'tools');
export const mockCommunicationApps = mockApps.filter(app => app.category === 'communication');
export const mockSettingsApps = mockApps.filter(app => app.category === 'settings');
