/**
 * Market Service - Core business logic for marketplace operations
 */

import {
  MarketService as IMarketService,
  MarketRepository,
  VendorRepository,
  ItemRepository,
  InventoryRepository
} from './interfaces';
import { Item, Market, Vendor, MarketType, Requirement } from './types';

export class MarketService implements IMarketService {
  constructor(
    private marketRepository: MarketRepository,
    private vendorRepository: VendorRepository,
    private itemRepository: ItemRepository,
    private inventoryRepository: InventoryRepository
  ) {}

  /**
   * Check if player meets requirements to access a market
   */
  async checkMarketAccess(marketId: string): Promise<boolean> {
    const market = await this.marketRepository.getMarketById(marketId);
    if (!market) {
      return false;
    }

    // Check each requirement
    for (const requirement of market.accessRequirements) {
      const meetsRequirement = await this.checkRequirement(requirement);
      if (!meetsRequirement) {
        return false;
      }
    }

    return true;
  }

  /**
   * Process a purchase transaction
   */
  async purchaseItem(itemId: string): Promise<boolean> {
    try {
      const item = await this.itemRepository.getItemById(itemId);
      if (!item || !item.available) {
        return false;
      }

      // Check if player meets item requirements
      const meetsRequirements = await this.checkItemRequirements(itemId);
      if (!meetsRequirements) {
        return false;
      }

      // Check if player has enough resources
      const playerResources = await this.inventoryRepository.getPlayerResources();
      const cost = item.discount ? item.cost * (1 - item.discount / 100) : item.cost;
      
      if (playerResources.credits < cost) {
        return false;
      }

      // Process the transaction
      await this.inventoryRepository.updatePlayerResources({
        credits: playerResources.credits - cost
      });

      await this.inventoryRepository.addToInventory(item);

      // Update item availability if it has limited stock
      if (item.stock && item.stock > 0) {
        await this.itemRepository.updateItem(itemId, {
          stock: item.stock - 1,
          available: item.stock - 1 > 0
        });
      }

      return true;
    } catch (error) {
      console.error('Purchase failed:', error);
      return false;
    }
  }

  /**
   * Sell an item from player's inventory
   */
  async sellItem(itemId: string): Promise<boolean> {
    try {
      const item = await this.itemRepository.getItemById(itemId);
      if (!item) {
        return false;
      }

      // Calculate sell price (typically 50-70% of purchase price)
      const sellPrice = Math.floor(item.cost * 0.6);

      // Remove from inventory and add credits
      await this.inventoryRepository.removeFromInventory(itemId);
      
      const playerResources = await this.inventoryRepository.getPlayerResources();
      await this.inventoryRepository.updatePlayerResources({
        credits: playerResources.credits + sellPrice
      });

      return true;
    } catch (error) {
      console.error('Sale failed:', error);
      return false;
    }
  }

  /**
   * Get available items that match player's search criteria
   */
  async searchForItems(searchText: string, filters?: Partial<Item>): Promise<Item[]> {
    const searchCriteria = {
      ...filters,
      available: true
    };

    let items = await this.itemRepository.searchItems(searchCriteria);

    // Filter by search text if provided
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      items = items.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchLower)))
      );
    }

    return items;
  }

  /**
   * Check if player meets requirements for an item
   */
  async checkItemRequirements(itemId: string): Promise<boolean> {
    const item = await this.itemRepository.getItemById(itemId);
    if (!item || !item.requirements) {
      return true;
    }

    // Check each requirement
    for (const requirement of item.requirements) {
      const meetsRequirement = await this.checkRequirement(requirement);
      if (!meetsRequirement) {
        return false;
      }
    }

    return true;
  }

  /**
   * Refresh market offerings (triggered by game time or events)
   */
  async refreshMarketOfferings(): Promise<void> {
    try {
      await this.marketRepository.refreshMarkets();
      
      // Additional logic for refreshing vendor inventories, prices, etc.
      // This would typically involve:
      // 1. Updating item availability
      // 2. Adjusting prices based on market conditions
      // 3. Adding new items or removing old ones
      // 4. Updating vendor relationships
      
    } catch (error) {
      console.error('Failed to refresh market offerings:', error);
    }
  }

  /**
   * Get special deals or discounted items
   */
  async getSpecialOffers(): Promise<Item[]> {
    const allItems = await this.itemRepository.searchItems({ available: true });
    
    // Filter items with discounts or special conditions
    return allItems.filter(item => 
      item.discount && item.discount > 0
    ).sort((a, b) => (b.discount || 0) - (a.discount || 0));
  }

  /**
   * Helper method to check if a requirement is met
   */
  private async checkRequirement(requirement: Requirement): Promise<boolean> {
    const playerResources = await this.inventoryRepository.getPlayerResources();
    
    switch (requirement.type) {
      case 'level':
        return (playerResources.level || 0) >= Number(requirement.value);
      
      case 'reputation':
        return (playerResources.reputation || 0) >= Number(requirement.value);
      
      case 'skill':
        // This would need to be implemented based on your skill system
        return true; // Placeholder
      
      case 'item':
        const inventory = await this.inventoryRepository.getPlayerInventory();
        return inventory.some(item => item.id === requirement.value);
      
      default:
        return true;
    }
  }
}
