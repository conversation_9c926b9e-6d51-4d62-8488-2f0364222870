.missionPage {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
}

.missionHeader {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(0, 179, 164, 0.2);
}

.missionTitle {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.welcomeMessage {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
}

.missionContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.filters {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.filterButton {
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-hover);
  border: 1px solid rgba(0, 179, 164, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.filterButton:hover {
  background: rgba(0, 179, 164, 0.1);
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

.filterButton.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--bg-primary);
}

.listContainer, .detailContainer {
  height: 100%;
}
