/**
 * Mock Market Repository implementations for local development
 */

import {
  MarketRepository,
  VendorRepository,
  ItemRepository,
  InventoryRepository
} from '@/core/market/interfaces';
import { Item, Market, Vendor, MarketType } from '@/core/market/types';
import { mockMarketItems, mockMarketCategories } from '@/shared/data/mock';

/**
 * Mock implementation of Market Repository
 */
export class MockMarketRepository implements MarketRepository {
  private markets: Record<string, Market> = {};

  constructor() {
    this.initializeSampleMarkets();
  }

  private initializeSampleMarkets() {
    const sampleMarkets: Market[] = [
      {
        id: 'public-market',
        name: 'Public Market',
        description: 'Legal marketplace for standard cybersecurity tools and services',
        type: 'public',
        accessRequirements: [],
        vendors: ['vendor-1', 'vendor-2'],
        isAccessible: true,
        lastRefresh: new Date().toISOString()
      },
      {
        id: 'gray-market',
        name: 'Gray Market',
        description: 'Semi-legal marketplace for advanced tools and questionable services',
        type: 'gray',
        accessRequirements: [
          { type: 'level', value: 5, description: 'Minimum level 5 required' },
          { type: 'reputation', value: 100, description: 'Minimum reputation 100 required' }
        ],
        vendors: ['vendor-3', 'vendor-4'],
        isAccessible: true,
        lastRefresh: new Date().toISOString()
      },
      {
        id: 'dark-market',
        name: 'Dark Market',
        description: 'Illegal marketplace for dangerous tools and stolen data',
        type: 'dark',
        accessRequirements: [
          { type: 'level', value: 10, description: 'Minimum level 10 required' },
          { type: 'reputation', value: 500, description: 'Minimum reputation 500 required' },
          { type: 'item', value: 'dark-web-access', description: 'Dark web access required' }
        ],
        vendors: ['vendor-5', 'vendor-6'],
        isAccessible: false,
        lastRefresh: new Date().toISOString()
      }
    ];

    sampleMarkets.forEach(market => {
      this.markets[market.id] = market;
    });
  }

  async getAllMarkets(): Promise<Market[]> {
    return Object.values(this.markets);
  }

  async getMarketById(id: string): Promise<Market | null> {
    return this.markets[id] || null;
  }

  async getMarketsByType(type: MarketType): Promise<Market[]> {
    return Object.values(this.markets).filter(market => market.type === type);
  }

  async getAccessibleMarkets(): Promise<Market[]> {
    return Object.values(this.markets).filter(market => market.isAccessible);
  }

  async refreshMarkets(): Promise<void> {
    // Update last refresh time for all markets
    Object.values(this.markets).forEach(market => {
      market.lastRefresh = new Date().toISOString();
    });
  }
}

/**
 * Mock implementation of Vendor Repository
 */
export class MockVendorRepository implements VendorRepository {
  private vendors: Record<string, Vendor> = {};

  constructor() {
    this.initializeSampleVendors();
  }

  private initializeSampleVendors() {
    const sampleVendors: Vendor[] = [
      {
        id: 'vendor-1',
        name: 'SecureTech Solutions',
        description: 'Legitimate cybersecurity vendor specializing in enterprise solutions',
        reputation: 95,
        marketType: 'public',
        specialties: ['software', 'service'],
        trustLevel: 10,
        lastSeen: new Date().toISOString()
      },
      {
        id: 'vendor-2',
        name: 'CyberDefense Corp',
        description: 'Professional security services and training provider',
        reputation: 88,
        marketType: 'public',
        specialties: ['software', 'infrastructure'],
        trustLevel: 9,
        lastSeen: new Date().toISOString()
      },
      {
        id: 'vendor-3',
        name: 'Anonymous Collective',
        description: 'Underground group providing advanced hacking tools',
        reputation: 75,
        marketType: 'gray',
        specialties: ['software', 'data'],
        trustLevel: 6,
        lastSeen: new Date().toISOString()
      }
    ];

    sampleVendors.forEach(vendor => {
      this.vendors[vendor.id] = vendor;
    });
  }

  async getVendorsByMarket(marketId: string): Promise<Vendor[]> {
    const market = await new MockMarketRepository().getMarketById(marketId);
    if (!market) return [];

    return market.vendors.map(vendorId => this.vendors[vendorId]).filter(Boolean);
  }

  async getVendorById(id: string): Promise<Vendor | null> {
    return this.vendors[id] || null;
  }

  async getAccessibleVendors(marketId: string): Promise<Vendor[]> {
    return this.getVendorsByMarket(marketId);
  }

  async updateVendorRelationship(vendorId: string, newLevel: number): Promise<void> {
    if (this.vendors[vendorId]) {
      this.vendors[vendorId].trustLevel = newLevel;
    }
  }
}

/**
 * Mock implementation of Item Repository
 */
export class MockItemRepository implements ItemRepository {
  private items: Record<string, Item> = {};

  constructor() {
    this.initializeSampleItems();
  }

  private initializeSampleItems() {
    // Convert mock data to proper Item format
    const allItems = Object.values(mockMarketItems).flat();
    
    allItems.forEach(mockItem => {
      const item: Item = {
        id: mockItem.id,
        name: mockItem.name,
        description: mockItem.description,
        category: mockItem.category || 'software',
        rarity: mockItem.rarity,
        legality: mockItem.legality || 'legal',
        cost: mockItem.price,
        discount: mockItem.discount,
        requirements: [],
        capabilities: [],
        marketSource: mockItem.marketSource || 'public',
        vendorId: 'vendor-1', // Default vendor
        available: mockItem.available !== false,
        stock: 10, // Default stock
        tags: []
      };

      this.items[item.id] = item;
    });
  }

  async getItemsByVendor(vendorId: string): Promise<Item[]> {
    return Object.values(this.items).filter(item => item.vendorId === vendorId);
  }

  async getItemById(id: string): Promise<Item | null> {
    return this.items[id] || null;
  }

  async searchItems(criteria: Partial<Item>): Promise<Item[]> {
    return Object.values(this.items).filter(item => {
      return Object.entries(criteria).every(([key, value]) => {
        if (value === undefined) return true;
        return (item as any)[key] === value;
      });
    });
  }

  async updateItem(id: string, updates: Partial<Item>): Promise<void> {
    if (this.items[id]) {
      this.items[id] = { ...this.items[id], ...updates };
    }
  }
}

/**
 * Mock implementation of Inventory Repository
 */
export class MockInventoryRepository implements InventoryRepository {
  private inventory: Item[] = [];
  private resources = {
    credits: 50000,
    level: 5,
    reputation: 150
  };

  async getPlayerInventory(): Promise<Item[]> {
    return [...this.inventory];
  }

  async addToInventory(item: Item): Promise<void> {
    this.inventory.push(item);
  }

  async removeFromInventory(itemId: string): Promise<void> {
    this.inventory = this.inventory.filter(item => item.id !== itemId);
  }

  async getPlayerResources(): Promise<Record<string, number>> {
    return { ...this.resources };
  }

  async updatePlayerResources(updates: Record<string, number>): Promise<void> {
    this.resources = { ...this.resources, ...updates };
  }
}
