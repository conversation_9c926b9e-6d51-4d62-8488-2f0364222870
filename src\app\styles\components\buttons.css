/* Modern Buttons */
button, .button {
  cursor: pointer;
  font-family: var(--font-main);
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5em 1em;
  border: none;

  border-radius: var(--border-radius-lg);
  /* border: 1px solid var(--border-subtle); */
  background: rgba(255, 255, 255, 0.02);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  letter-spacing: -0.025em;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

button::before, .button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

button:hover::before, .button:hover::before {
  left: 100%;
}

button:hover, .button:hover {
  border-color: var(--border-active);
  background: rgba(255, 255, 255, 0.05);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

button:active, .button:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

.button-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-500));
  color: var(--text-on-accent);
  border: 1px solid var(--primary-500);
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.button-primary:hover {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-400));
  border-color: var(--primary-400);
  box-shadow: var(--shadow-lg);
}

.button-danger {
  background: linear-gradient(135deg, var(--color-error), #dc2626);
  color: var(--text-on-accent);
  border: 1px solid var(--color-error);
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.button-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-color: #dc2626;
  box-shadow: var(--shadow-lg);
}

/* Disabled state */
button:disabled,
.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled::before,
.button:disabled::before {
  display: none;
}

/* Button sizes */
.button-sm {
  font-size: 0.8rem;
  padding: 0.3em 0.8em;
}

.button-lg {
  font-size: 1rem;
  padding: 0.7em 1.2em;
}

/* Icon buttons */
.button-icon {
  padding: 0.5em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button-icon svg {
  width: 1.2em;
  height: 1.2em;
}
