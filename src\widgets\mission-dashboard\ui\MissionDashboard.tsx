import React, { useEffect, useState } from 'react';
import { MissionCard } from '@/features/mission';
import { MissionList } from '@/features/mission';
import { MissionDetail } from '@/features/mission';
import { useMissionStore, useMissionSelectors } from '@/features/mission/model/store';
import styles from './MissionDashboard.module.css';

interface MissionDashboardProps {
  className?: string;
  showDetails?: boolean;
}

const MissionDashboard: React.FC<MissionDashboardProps> = ({
  showDetails = false
}) => {
  const [selectedMissionId, setSelectedMissionId] = useState<string | null>(null);

  // Mission store
  const { fetchMissions, setActiveMission } = useMissionStore();
  const { availableMissions, activeMission, isLoading } = useMissionSelectors();

  // Fetch missions on mount
  useEffect(() => {
    fetchMissions();
  }, [fetchMissions]);

  const handleMissionSelect = (missionId: string) => {
    setSelectedMissionId(missionId);
    setActiveMission(missionId);
  };

  return (
    <div className={styles.missionDashboard}>
      <div className={styles.missionHeader}>
        <h1 className={styles.missionTitle}>Mission Control</h1>
        <span className={styles.welcomeMessage}>Select and manage your operations</span>
      </div>

      <div className={styles.missionContent}>
        <div className={styles.missionList}>
          <MissionList
            missions={availableMissions}
            onMissionSelect={handleMissionSelect}
            loading={isLoading}
            title="Available Missions"
          />
        </div>

        {showDetails && selectedMissionId && activeMission && (
          <div className={styles.missionDetail}>
            <MissionDetail
              mission={activeMission}
              onBack={() => {
                setSelectedMissionId(null);
                setActiveMission(null);
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MissionDashboard;