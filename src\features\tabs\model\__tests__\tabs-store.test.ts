import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useTabsStore } from '../store'
import { Tab } from '@/shared/types/common'

describe('TabsStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useTabsStore.setState({
      tabs: [
        {
          id: 'dashboard',
          title: 'Dashboard',
          closable: false,
          type: 'dashboard',
        },
      ],
      activeTabId: 'dashboard',
      layout: 'single',
    })
  })

  describe('addTab', () => {
    it('should add a new tab', () => {
      const { result } = renderHook(() => useTabsStore())
      
      const newTab: Tab = {
        id: 'test-tab',
        title: 'Test Tab',
        type: 'terminal',
        closable: true
      }

      act(() => {
        result.current.addTab(newTab)
      })

      expect(result.current.tabs).toHaveLength(2)
      expect(result.current.tabs[1]).toEqual(newTab)
      expect(result.current.activeTabId).toBe('test-tab')
    })

    it('should not add duplicate tabs', () => {
      const { result } = renderHook(() => useTabsStore())
      
      const tab: Tab = {
        id: 'terminal-1',
        title: 'Terminal',
        type: 'terminal',
        closable: true,
        data: { instanceId: 'default' }
      }

      // Add tab twice
      act(() => {
        result.current.addTab(tab)
      })
      
      act(() => {
        result.current.addTab(tab)
      })

      // Should only have 2 tabs (dashboard + terminal)
      expect(result.current.tabs).toHaveLength(2)
      expect(result.current.activeTabId).toBe('terminal-1')
    })

    it('should update layout based on tab count', () => {
      const { result } = renderHook(() => useTabsStore())

      // Add tabs to test layout changes
      act(() => {
        result.current.addTab({ id: 'tab1', title: 'Tab 1', type: 'terminal', closable: true })
      })
      expect(result.current.layout).toBe('split') // 2 tabs (dashboard + tab1)

      act(() => {
        result.current.addTab({ id: 'tab2', title: 'Tab 2', type: 'mission', closable: true })
      })
      expect(result.current.layout).toBe('triple') // 3 tabs

      act(() => {
        result.current.addTab({ id: 'tab3', title: 'Tab 3', type: 'team', closable: true })
      })
      expect(result.current.layout).toBe('grid') // 4 tabs
    })
  })

  describe('removeTab', () => {
    it('should remove a tab', () => {
      const { result } = renderHook(() => useTabsStore())
      
      // Add a tab first
      act(() => {
        result.current.addTab({
          id: 'removable-tab',
          title: 'Removable',
          type: 'terminal',
          closable: true
        })
      })

      // Remove the tab
      act(() => {
        result.current.removeTab('removable-tab')
      })

      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('dashboard')
      expect(result.current.activeTabId).toBe('dashboard')
    })

    it('should update active tab when removing active tab', () => {
      const { result } = renderHook(() => useTabsStore())
      
      // Add multiple tabs
      act(() => {
        result.current.addTab({ id: 'tab1', title: 'Tab 1', type: 'terminal', closable: true })
        result.current.addTab({ id: 'tab2', title: 'Tab 2', type: 'mission', closable: true })
      })

      // Remove the active tab (tab2)
      act(() => {
        result.current.removeTab('tab2')
      })

      expect(result.current.activeTabId).toBe('dashboard')
    })

    it('should update layout when removing tabs', () => {
      const { result } = renderHook(() => useTabsStore())

      // Add multiple tabs
      act(() => {
        result.current.addTab({ id: 'tab1', title: 'Tab 1', type: 'terminal', closable: true })
        result.current.addTab({ id: 'tab2', title: 'Tab 2', type: 'mission', closable: true })
        result.current.addTab({ id: 'tab3', title: 'Tab 3', type: 'team', closable: true })
      })

      expect(result.current.layout).toBe('grid') // 4 tabs total

      // Remove a tab
      act(() => {
        result.current.removeTab('tab3')
      })

      expect(result.current.layout).toBe('triple') // 3 tabs remaining
    })
  })

  describe('toggleTab', () => {
    it('should add tab if it does not exist', () => {
      const { result } = renderHook(() => useTabsStore())

      const newTab = {
        id: 'toggle-tab',
        title: 'Toggle Tab',
        type: 'terminal',
        closable: true
      }

      act(() => {
        result.current.toggleTab(newTab)
      })

      expect(result.current.tabs).toHaveLength(2) // dashboard + new tab
      expect(result.current.tabs[1]).toEqual(newTab)
      expect(result.current.activeTabId).toBe('toggle-tab')
    })

    it('should remove tab if it exists and is closable', () => {
      const { result } = renderHook(() => useTabsStore())

      const newTab = {
        id: 'toggle-tab',
        title: 'Toggle Tab',
        type: 'terminal',
        closable: true
      }

      // Add tab first
      act(() => {
        result.current.addTab(newTab)
      })

      expect(result.current.tabs).toHaveLength(2)

      // Toggle it (should remove)
      act(() => {
        result.current.toggleTab(newTab)
      })

      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('dashboard')
    })

    it('should not remove dashboard tab when toggled', () => {
      const { result } = renderHook(() => useTabsStore())

      const dashboardTab = {
        id: 'dashboard',
        title: 'Dashboard',
        type: 'dashboard',
        closable: false
      }

      // Toggle dashboard (should just set as active)
      act(() => {
        result.current.toggleTab(dashboardTab)
      })

      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.activeTabId).toBe('dashboard')
    })
  })

  describe('setActiveTab', () => {
    it('should set active tab', () => {
      const { result } = renderHook(() => useTabsStore())

      // Add a tab
      act(() => {
        result.current.addTab({
          id: 'new-active-tab',
          title: 'New Active',
          type: 'terminal',
          closable: true
        })
      })

      // Set dashboard as active
      act(() => {
        result.current.setActiveTab('dashboard')
      })

      expect(result.current.activeTabId).toBe('dashboard')
    })
  })

  describe('updateTab', () => {
    it('should update tab properties', () => {
      const { result } = renderHook(() => useTabsStore())
      
      // Add a tab
      act(() => {
        result.current.addTab({
          id: 'updatable-tab',
          title: 'Original Title',
          type: 'terminal',
          closable: true
        })
      })

      // Update the tab
      act(() => {
        result.current.updateTab('updatable-tab', {
          title: 'Updated Title'
        })
      })

      const updatedTab = result.current.tabs.find(tab => tab.id === 'updatable-tab')
      expect(updatedTab?.title).toBe('Updated Title')
    })
  })

  describe('setLayout', () => {
    it('should set layout manually', () => {
      const { result } = renderHook(() => useTabsStore())

      act(() => {
        result.current.setLayout('grid-3x3')
      })

      expect(result.current.layout).toBe('grid-3x3')
    })
  })

  describe('clearTabs', () => {
    it('should reset to initial state', () => {
      const { result } = renderHook(() => useTabsStore())
      
      // Add multiple tabs
      act(() => {
        result.current.addTab({ id: 'tab1', title: 'Tab 1', type: 'terminal', closable: true })
        result.current.addTab({ id: 'tab2', title: 'Tab 2', type: 'mission', closable: true })
      })

      // Clear all tabs
      act(() => {
        result.current.clearTabs()
      })

      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('dashboard')
      expect(result.current.activeTabId).toBe('dashboard')
      expect(result.current.layout).toBe('single')
    })
  })
})
