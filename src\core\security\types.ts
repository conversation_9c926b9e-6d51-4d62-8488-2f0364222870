/**
 * Core domain types for Security/OPSEC functionality
 */

export type ThreatSeverity = 'low' | 'medium' | 'high' | 'critical';
export type ThreatType = 'surveillance' | 'forensic' | 'network' | 'physical' | 'social';
export type ThreatStatus = 'active' | 'mitigated' | 'investigating';
export type CountermeasureType = 'preventive' | 'detective' | 'corrective';

export interface SecurityThreat {
  id: string;
  name: string;
  description: string;
  severity: ThreatSeverity;
  type: ThreatType;
  detectedAt: string;
  status: ThreatStatus;
  source?: string;
  recommendations: string[];
  affectedSystems?: string[];
  estimatedImpact?: number; // 1-10 scale
}

export interface SecurityMetrics {
  overallLevel: number;
  networkSecurity: number;
  physicalSecurity: number;
  operationalSecurity: number;
  digitalFootprint: number;
  traceLevel: number;
  lastAssessment: string;
}

export interface Countermeasure {
  id: string;
  name: string;
  description: string;
  type: CountermeasureType;
  cost: number;
  effectiveness: number; // 0-100 percentage
  duration: number; // in hours
  requirements: string[];
  active: boolean;
  cooldown?: number; // hours before can be used again
}

export interface SecurityAlert {
  id: string;
  title: string;
  message: string;
  severity: ThreatSeverity;
  timestamp: string;
  acknowledged: boolean;
  threatId?: string;
}

export interface SecurityAssessment {
  id: string;
  timestamp: string;
  overallScore: number;
  vulnerabilities: SecurityVulnerability[];
  recommendations: string[];
  nextAssessmentDue: string;
}

export interface SecurityVulnerability {
  id: string;
  name: string;
  description: string;
  severity: ThreatSeverity;
  category: ThreatType;
  exploitability: number; // 1-10 scale
  impact: number; // 1-10 scale
  mitigation: string;
}

export interface OperationalSecurity {
  communicationSecurity: number;
  dataProtection: number;
  accessControl: number;
  auditTrail: number;
  incidentResponse: number;
}

export interface SecurityEvent {
  id: string;
  type: 'threat_detected' | 'countermeasure_activated' | 'vulnerability_found' | 'assessment_completed';
  timestamp: string;
  description: string;
  severity: ThreatSeverity;
  metadata?: Record<string, any>;
}
