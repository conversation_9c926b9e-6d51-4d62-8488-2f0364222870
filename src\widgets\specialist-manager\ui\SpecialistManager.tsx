import React from 'react';
import { SpecialistList } from '../../../features/specialist/ui/specialist-list';
import { SpecialistDetail } from '../../../features/specialist/ui/specialist-detail';
import styles from './SpecialistManager.module.css';

interface SpecialistManagerProps {
  className?: string;
}

const SpecialistManager: React.FC<SpecialistManagerProps> = ({ className }) => {
  const [selectedSpecialistId, setSelectedSpecialistId] = React.useState<string | null>(null);

  return (
    <div className={styles.specialistManager}>
      <div className={styles.specialistHeader}>
        <h1 className={styles.specialistTitle}>Team Management</h1>
        <span className={styles.welcomeMessage}>Recruit and manage your specialist team</span>
      </div>

      <div className={styles.specialistContent}>
        <div className={styles.specialistList}>
          <SpecialistList
            onSelectSpecialist={(specialistId) => setSelectedSpecialistId(specialistId)}
            selectedSpecialistId={selectedSpecialistId}
          />
        </div>

        {selectedSpecialistId && (
          <div className={styles.specialistDetail}>
            <SpecialistDetail specialistId={selectedSpecialistId} />
          </div>
        )}
      </div>
    </div>
  );
};

export default SpecialistManager;