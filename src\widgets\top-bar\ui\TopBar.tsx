import React, { useState } from 'react';
import styles from './TopBar.module.css';
import {
  IconBell,
  IconClock,
  IconCalendar,
  IconSettings,
  IconShield,
  IconCoin
} from '@tabler/icons-react';
import { useGameTime } from '../../../shared/hooks/useGameTime';
import { AnimatedBackground } from '@/shared/ui/AnimatedBackground';

// Widget definitions
interface Widget {
  id: string;
  name: string;
  component: React.ReactNode;
  enabled: boolean;
}

// Example widgets
const TimeWidget: React.FC = () => {
  const { currentTime } = useGameTime();

  // Calculate minutes from ticks within current hour
  // Since we have 60 ticks per hour, each tick = 1 minute
  const ticksInCurrentHour = currentTime.totalTicks % 60;
  const minutes = ticksInCurrentHour;
  const hours = currentTime.hours;

  return (
    <div className={styles.timeWidget}>
      <IconClock size={16} stroke={1.5} />
      <span>Day {currentTime.days + 1}, {String(hours).padStart(2, '0')}:{String(minutes).padStart(2, '0')}</span>
    </div>
  );
};

const DateWidget: React.FC = () => {
  const currentDate = new Date();
  
  const formattedDate = currentDate.toLocaleDateString([], { 
    weekday: 'short',
    month: 'short', 
    day: 'numeric' 
  });
  
  return (
    <div className={styles.dateWidget}>
      <IconCalendar size={16} stroke={1.5} />
      <span>{formattedDate}</span>
    </div>
  );
};

const SecurityLevelWidget: React.FC = () => {
  return (
    <div className={styles.securityWidget}>
      <IconShield size={16} stroke={1.5} />
      <span>100%</span>
    </div>
  );
};

const CreditsWidget: React.FC = () => {
  return (
    <div className={styles.creditsWidget}>
      <IconCoin size={16} stroke={1.5} />
      <span>1,000</span>
    </div>
  );
};

const availableWidgets: Widget[] = [
  { id: 'time', name: 'Time', component: null, enabled: true },
  { id: 'date', name: 'Date', component: null, enabled: true },
  { id: 'security', name: 'Security Level', component: null, enabled: true },
  { id: 'credits', name: 'Credits', component: null, enabled: true },
];

const getWidgetComponent = (widgetId: string) => {
  switch (widgetId) {
    case 'time':
      return <TimeWidget />;
    case 'date':
      return <DateWidget />;
    case 'security':
      return <SecurityLevelWidget />;
    case 'credits':
      return <CreditsWidget />;
    default:
      return null;
  }
};

const TopBar: React.FC = () => {
  const [widgets, setWidgets] = useState(availableWidgets);
  const [showNotifications, setShowNotifications] = useState(false);
  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(true);
  const [isConfiguring, setIsConfiguring] = useState(false);
  
  const toggleWidget = (widgetId: string) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === widgetId ? { ...widget, enabled: !widget.enabled } : widget
    ));
  };
  
  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    if (hasUnreadNotifications) {
      setHasUnreadNotifications(false);
    }
  };
  
  return (
    <div className={styles.topBar}>
        <AnimatedBackground />

      <div className={styles.topBarContent}>
        <div className={styles.brand}>
          <span className={styles.name}>Karrot OS</span>
        </div>

        <div className={styles.widgetsArea}>
          {widgets.filter(w => w.enabled).map(widget => (
            <div key={widget.id} className={styles.widget}>
              {getWidgetComponent(widget.id)}
            </div>
          ))}
        </div>

        <div className={styles.actionArea}>
          <button
            className={`${styles.actionButton} ${isConfiguring ? styles.active : ''}`}
            onClick={() => setIsConfiguring(!isConfiguring)}
            title="Configure Widgets"
          >
            <IconSettings size={18} stroke={1.5} />
          </button>

          <button
            className={`${styles.actionButton} ${showNotifications ? styles.active : ''}`}
            onClick={toggleNotifications}
            title="Notifications"
          >
            <IconBell size={18} stroke={1.5} />
            {hasUnreadNotifications && <div className={styles.notificationDot}></div>}
          </button>
        </div>
      </div>
      
      {/* Configuration Panel */}
      {isConfiguring && (
        <div className={styles.configPanel}>
          <h3>Configure Widgets</h3>
          <ul className={styles.widgetList}>
            {widgets.map(widget => (
              <li key={widget.id} className={styles.widgetItem}>
                <label className={styles.widgetToggle}>
                  <input 
                    type="checkbox" 
                    checked={widget.enabled}
                    onChange={() => toggleWidget(widget.id)}
                  />
                  <span className={styles.widgetName}>{widget.name}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TopBar;