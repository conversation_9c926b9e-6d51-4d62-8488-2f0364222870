/**
 * Mock Security Repository implementation for local development
 */

import { SecurityRepository } from '@/core/security/interfaces';
import {
  SecurityThreat,
  SecurityMetrics,
  Countermeasure,
  SecurityAlert,
  SecurityAssessment,
  SecurityEvent
} from '@/core/security/types';
import {
  mockSecurityThreats,
  mockSecurityMetrics,
  mockCountermeasures
} from '@/shared/data/mock';

/**
 * Mock implementation of Security Repository
 */
export class MockSecurityRepository implements SecurityRepository {
  private threats: Record<string, SecurityThreat> = {};
  private countermeasures: Record<string, Countermeasure> = {};
  private alerts: Record<string, SecurityAlert> = {};
  private assessments: SecurityAssessment[] = [];
  private events: SecurityEvent[] = [];
  private metrics: SecurityMetrics;

  constructor() {
    this.initializeData();
  }

  private initializeData() {
    // Initialize threats
    mockSecurityThreats.forEach(threat => {
      this.threats[threat.id] = { ...threat };
    });

    // Initialize countermeasures
    mockCountermeasures.forEach(countermeasure => {
      this.countermeasures[countermeasure.id] = { ...countermeasure };
    });

    // Initialize metrics
    this.metrics = { ...mockSecurityMetrics };
  }

  /**
   * Threat management
   */
  async getThreats(): Promise<SecurityThreat[]> {
    return Object.values(this.threats);
  }

  async getThreatById(id: string): Promise<SecurityThreat | null> {
    return this.threats[id] || null;
  }

  async createThreat(threat: Omit<SecurityThreat, 'id'>): Promise<SecurityThreat> {
    const newThreat: SecurityThreat = {
      ...threat,
      id: `threat-${Date.now()}`
    };
    
    this.threats[newThreat.id] = newThreat;
    return newThreat;
  }

  async updateThreat(id: string, updates: Partial<SecurityThreat>): Promise<void> {
    if (this.threats[id]) {
      this.threats[id] = { ...this.threats[id], ...updates };
    }
  }

  /**
   * Countermeasures
   */
  async getCountermeasures(): Promise<Countermeasure[]> {
    return Object.values(this.countermeasures);
  }

  async getCountermeasureById(id: string): Promise<Countermeasure | null> {
    return this.countermeasures[id] || null;
  }

  async activateCountermeasure(id: string): Promise<void> {
    if (this.countermeasures[id]) {
      this.countermeasures[id].active = true;
    }
  }

  async deactivateCountermeasure(id: string): Promise<void> {
    if (this.countermeasures[id]) {
      this.countermeasures[id].active = false;
    }
  }

  /**
   * Security metrics
   */
  async getSecurityMetrics(): Promise<SecurityMetrics> {
    return { ...this.metrics };
  }

  async updateSecurityMetrics(updates: Partial<SecurityMetrics>): Promise<void> {
    this.metrics = { ...this.metrics, ...updates };
  }

  /**
   * Alerts
   */
  async getAlerts(): Promise<SecurityAlert[]> {
    return Object.values(this.alerts);
  }

  async createAlert(alert: Omit<SecurityAlert, 'id'>): Promise<SecurityAlert> {
    const newAlert: SecurityAlert = {
      ...alert,
      id: `alert-${Date.now()}`
    };
    
    this.alerts[newAlert.id] = newAlert;
    return newAlert;
  }

  async acknowledgeAlert(id: string): Promise<void> {
    if (this.alerts[id]) {
      this.alerts[id].acknowledged = true;
    }
  }

  /**
   * Assessments
   */
  async getLatestAssessment(): Promise<SecurityAssessment | null> {
    if (this.assessments.length === 0) return null;
    
    return this.assessments.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )[0];
  }

  async createAssessment(assessment: Omit<SecurityAssessment, 'id'>): Promise<SecurityAssessment> {
    const newAssessment: SecurityAssessment = {
      ...assessment,
      id: `assessment-${Date.now()}`
    };
    
    this.assessments.push(newAssessment);
    return newAssessment;
  }

  /**
   * Events
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'id'>): Promise<void> {
    const newEvent: SecurityEvent = {
      ...event,
      id: `event-${Date.now()}`
    };
    
    this.events.unshift(newEvent); // Add to beginning for chronological order
    
    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(0, 1000);
    }
  }

  async getSecurityEvents(limit?: number): Promise<SecurityEvent[]> {
    return limit ? this.events.slice(0, limit) : this.events;
  }
}
