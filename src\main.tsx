import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './app/App';
import { initializeApplication } from './core/Application';

// Initialize the application before rendering
async function startApp() {
  try {
    // Initialize core services
    await initializeApplication({
      environment: import.meta.env.MODE === 'production' ? 'production' : 'development',
      enableNotifications: true,
      enableSecurityMonitoring: true,
      enableEventLogging: import.meta.env.MODE === 'development',
      autoCleanupInterval: 30
    });

    // Render the React app
    ReactDOM.createRoot(document.getElementById('root')!).render(
      <React.StrictMode>
        <App />
      </React.StrictMode>,
    );
  } catch (error) {
    console.error('Failed to start application:', error);

    // Show error message to user
    document.getElementById('root')!.innerHTML = `
      <div style="
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: #0a0a0a;
        color: #ff6b6b;
        font-family: monospace;
        text-align: center;
      ">
        <div>
          <h1>🚨 System Initialization Failed</h1>
          <p>Unable to start the cyber operations platform.</p>
          <p style="font-size: 0.8em; opacity: 0.7;">Check console for details.</p>
        </div>
      </div>
    `;
  }
}

// Start the application
startApp();
