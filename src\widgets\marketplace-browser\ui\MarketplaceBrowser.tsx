import React, { useState } from 'react';
import styles from './MarketplaceBrowser.module.css';
import { IconShoppingCart, IconSearch } from '@tabler/icons-react';
import clsx from 'clsx';
import { mockMarketItems, mockMarketCategories, MarketItem } from '@/shared/data/mock';

interface MarketplaceBrowserProps {
  className?: string;
}

// MarketItem interface is now imported from mock data

const MarketplaceBrowser: React.FC<MarketplaceBrowserProps> = ({ className }) => {
  const [activeCategory, setActiveCategory] = useState<string>('public');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // Use mock data from shared data
  const categories = mockMarketCategories;
  const marketItems = mockMarketItems;
  
  // Get items for the active category
  const activeItems = marketItems[activeCategory] || [];
  
  // Filter items based on search query
  const filteredItems = searchQuery
    ? activeItems.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : activeItems;
  
  return (
    <div className={clsx(styles.marketplaceBrowser, className)}>
      <div className={styles.header}>
        <h1 className={styles.title}>Marketplace</h1>
        <div className="search-bar">
          {/* Search functionality would go here */}
        </div>
      </div>
      
      <div className={styles.content}>
        <div className={styles.categories}>
          {categories.map(category => (
            <div 
              key={category.id}
              className={clsx(
                styles.category,
                activeCategory === category.id && styles.active
              )}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </div>
          ))}
        </div>
        
        <div className={styles.items}>
          {filteredItems.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>
                <IconShoppingCart size={48} stroke={1} />
              </div>
              <div className={styles.emptyText}>
                No items available in this marketplace.
              </div>
            </div>
          ) : (
            <div className={styles.itemGrid}>
              {filteredItems.map(item => (
                <div key={item.id} className={styles.itemCard}>
                  <div className={styles.itemImage} style={item.image ? { backgroundImage: `url(${item.image})` } : undefined}></div>
                  <div className={styles.itemContent}>
                    <h3 className={styles.itemTitle}>{item.name}</h3>
                    <p className={styles.itemDescription}>{item.description}</p>
                    <div className={styles.itemFooter}>
                      <span className={styles.itemPrice}>{item.price} ¢</span>
                      <span className={clsx(styles.itemRarity, styles[`rarity-${item.rarity}`])}>
                        {item.rarity}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketplaceBrowser;