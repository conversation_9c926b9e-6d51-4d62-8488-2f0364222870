/**
 * Mock mission data for development and testing
 */

import { Mission, MissionType, MissionCategory, MissionDifficulty } from '@/core/mission/types';

export const mockMissions: Mission[] = [
  {
    id: 'mission-1',
    title: 'Data Breach Investigation',
    description: 'Analyze a compromised system to identify how attackers gained access and what data was stolen.',
    brief: 'A financial services company has detected unusual activity on their network. They suspect a data breach and need your expertise to investigate the attack, identify the entry point, and determine what information was compromised.',
    faction: 'Security Operations Center',
    type: 'forensic',
    category: 'defensive',
    objectives: [
      {
        id: 'obj-1-1',
        title: 'Analyze System Logs',
        description: 'Review system logs to identify suspicious activities and potential entry points.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-1-2',
        title: 'Identify Compromised Data',
        description: 'Determine what sensitive information may have been accessed or stolen.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-1-3',
        title: 'Document Attack Vector',
        description: 'Create a detailed report of how the attack was carried out.',
        status: 'pending',
        required: false,
      }
    ],
    rewards: {
      money: 15000,
      experience: 500,
      reputation: 25
    },
    riskLevel: 2,
    difficulty: 'professional',
    timeConstraints: null,
    requiredSpecializations: ['forensic_analysis', 'network_security'],
    requiredResources: [],
    status: 'available',
    location: 'Financial Services Company',
    targetSystem: 'Corporate Network'
  },
  {
    id: 'mission-2',
    title: 'Corporate Espionage',
    description: 'Infiltrate a competitor\'s network to gather intelligence on their upcoming product launch.',
    brief: 'A tech startup needs intelligence on their competitor\'s new product. Your mission is to gain access to their development servers and extract product specifications, launch timelines, and marketing strategies.',
    faction: 'Corporate Intelligence',
    type: 'intelligence',
    category: 'offensive',
    objectives: [
      {
        id: 'obj-2-1',
        title: 'Reconnaissance',
        description: 'Gather information about the target company\'s network infrastructure.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-2-2',
        title: 'Network Infiltration',
        description: 'Gain unauthorized access to the target\'s internal network.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-2-3',
        title: 'Data Extraction',
        description: 'Locate and extract product development documents and marketing plans.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-2-4',
        title: 'Cover Tracks',
        description: 'Remove all traces of the intrusion to avoid detection.',
        status: 'pending',
        required: false,
      }
    ],
    rewards: {
      money: 25000,
      experience: 750,
      reputation: 15
    },
    riskLevel: 7,
    difficulty: 'expert',
    timeConstraints: {
      totalTime: 2880, // 48 hours
      remaining: 2880,
      critical: 720 // 12 hours
    },
    requiredSpecializations: ['network_infiltration', 'social_engineering'],
    requiredResources: [
      { id: 'vpn-service', name: 'Anonymous VPN Service', quantity: 1 },
      { id: 'exploit-kit', name: 'Network Exploitation Kit', quantity: 1 }
    ],
    status: 'available',
    location: 'Tech Competitor HQ',
    targetSystem: 'Development Network'
  },
  {
    id: 'mission-3',
    title: 'Ransomware Recovery',
    description: 'Help a hospital recover from a ransomware attack and strengthen their defenses.',
    brief: 'A major hospital has been hit by ransomware, encrypting critical patient data systems. They need immediate assistance to recover their systems and implement better security measures to prevent future attacks.',
    faction: 'Healthcare Security Alliance',
    type: 'defense',
    category: 'defensive',
    objectives: [
      {
        id: 'obj-3-1',
        title: 'System Assessment',
        description: 'Evaluate the extent of the ransomware infection and identify affected systems.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-3-2',
        title: 'Data Recovery',
        description: 'Attempt to recover encrypted data using available backups and decryption tools.',
        status: 'pending',
        required: true,
      },
      {
        id: 'obj-3-3',
        title: 'Security Hardening',
        description: 'Implement improved security measures to prevent future ransomware attacks.',
        status: 'pending',
        required: true,
      }
    ],
    rewards: {
      money: 20000,
      experience: 600,
      reputation: 40
    },
    riskLevel: 3,
    difficulty: 'professional',
    timeConstraints: {
      totalTime: 1440, // 24 hours
      remaining: 1440,
      critical: 360 // 6 hours
    },
    requiredSpecializations: ['malware_analysis', 'system_administration'],
    requiredResources: [
      { id: 'decryption-tools', name: 'Advanced Decryption Tools', quantity: 1 },
      { id: 'backup-system', name: 'Emergency Backup System', quantity: 1 }
    ],
    status: 'available',
    location: 'Regional Medical Center',
    targetSystem: 'Hospital Information System'
  }
];

export const mockActiveMissions = [
  {
    id: 'mission-1',
    title: 'Data Extraction',
    faction: 'Crime Syndicate',
    difficulty: 'professional' as MissionDifficulty,
    progress: 60
  },
  {
    id: 'mission-2',
    title: 'System Infiltration',
    faction: 'State-Sponsored',
    difficulty: 'expert' as MissionDifficulty,
    progress: 25
  }
];
