.specialistManager {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
}

.specialistHeader {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(0, 179, 164, 0.2);
}

.specialistTitle {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.welcomeMessage {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
}

.specialistContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  height: calc(100% - 80px);
}

.specialistList {
  flex: 1;
  overflow-y: auto;
}

.specialistDetail {
  flex: 1;
  overflow-y: auto;
  border-top: 1px solid rgba(0, 179, 164, 0.2);
  padding-top: var(--space-lg);
}

/* Responsive layout */
@media (min-width: 768px) {
  .specialistContent {
    flex-direction: row;
  }
  
  .specialistDetail {
    border-top: none;
    border-left: 1px solid rgba(0, 179, 164, 0.2);
    padding-top: 0;
    padding-left: var(--space-lg);
  }
}
