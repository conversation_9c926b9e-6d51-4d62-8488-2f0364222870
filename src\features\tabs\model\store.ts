import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { LayoutType, Tab, TabId, TabType } from '@/shared/types/common';

interface TabsState {
  tabs: Tab[];
  activeTabId: TabId;
  layout: LayoutType;
}

interface TabsActions {
  addTab: (tab: Tab) => void;
  removeTab: (tabId: TabId) => void;
  toggleTab: (tab: Tab) => void;
  setActiveTab: (tabId: TabId) => void;
  updateTab: (tabId: TabId, updates: Partial<Tab>) => void;
  setLayout: (layout: LayoutType) => void;
  clearTabs: () => void;
}

type TabsStore = TabsState & TabsActions;

const getLayoutForTabCount = (count: number): LayoutType => {
  if (count <= 1) return 'single';
  if (count === 2) return 'split';
  if (count === 3) return 'triple';
  if (count >= 4) return 'grid';
  return 'single';
};

const initialState: TabsState = {
  tabs: [
    {
      id: 'dashboard',
      title: 'Dashboard',
      closable: false,
      type: 'dashboard',
    },
  ],
  activeTabId: 'dashboard',
  layout: 'single',
};

export const useTabsStore = create<TabsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      addTab: (tab: Tab) => {
        const { tabs } = get();
        
        // Check if tab already exists
        const existingTabIndex = tabs.findIndex(existingTab => 
          existingTab.type === tab.type && 
          JSON.stringify(existingTab.data) === JSON.stringify(tab.data)
        );
        
        if (existingTabIndex !== -1) {
          // If tab exists, just set it as active
          set({ activeTabId: tabs[existingTabIndex].id });
        } else {
          // Add new tab
          const newTabs = [...tabs, tab];
          set({ 
            tabs: newTabs,
            activeTabId: tab.id,
            layout: getLayoutForTabCount(newTabs.length)
          });
        }
      },
      
      removeTab: (tabId: TabId) => {
        const { tabs, activeTabId } = get();
        const newTabs = tabs.filter(tab => tab.id !== tabId);

        let newActiveTabId = activeTabId;
        if (activeTabId === tabId && newTabs.length > 0) {
          // If we're removing the active tab, set the first remaining tab as active
          newActiveTabId = newTabs[0].id;
        }

        set({
          tabs: newTabs,
          activeTabId: newActiveTabId,
          layout: getLayoutForTabCount(newTabs.length)
        });
      },

      toggleTab: (tab: Tab) => {
        const { tabs } = get();

        // Check if tab already exists
        const existingTabIndex = tabs.findIndex(existingTab =>
          existingTab.id === tab.id ||
          (existingTab.type === tab.type &&
           JSON.stringify(existingTab.data) === JSON.stringify(tab.data))
        );

        if (existingTabIndex !== -1) {
          // Tab exists, remove it (unless it's dashboard which can't be closed)
          if (tabs[existingTabIndex].id !== 'dashboard' && tabs[existingTabIndex].closable !== false) {
            get().removeTab(tabs[existingTabIndex].id);
          } else {
            // If it's dashboard or non-closable, just set it as active
            set({ activeTabId: tabs[existingTabIndex].id });
          }
        } else {
          // Tab doesn't exist, add it
          get().addTab(tab);
        }
      },
      
      setActiveTab: (tabId: TabId) => {
        set({ activeTabId: tabId });
      },
      
      updateTab: (tabId: TabId, updates: Partial<Tab>) => {
        const { tabs } = get();
        const newTabs = tabs.map(tab => 
          tab.id === tabId ? { ...tab, ...updates } : tab
        );
        set({ tabs: newTabs });
      },
      
      setLayout: (layout: LayoutType) => {
        set({ layout });
      },
      
      clearTabs: () => {
        set(initialState);
      },
    }),
    {
      name: 'tabs-store',
    }
  )
);
