/**
 * Mock Notification Repository implementation for local development
 * This will be easily replaceable with API client for backend
 */

import { NotificationRepository } from '@/core/notification/interfaces';
import {
  Notification,
  NotificationSettings,
  NotificationTemplate,
  NotificationType,
  NotificationCategory
} from '@/core/notification/types';

/**
 * Mock implementation of Notification Repository
 */
export class MockNotificationRepository implements NotificationRepository {
  private notifications: Map<string, Notification> = new Map();
  private settings: NotificationSettings;
  private templates: Map<string, NotificationTemplate> = new Map();

  constructor() {
    this.initializeSettings();
    this.initializeTemplates();
  }

  private initializeSettings(): void {
    this.settings = {
      enabled: true,
      categories: {
        mission: true,
        team: true,
        market: true,
        security: true,
        system: true,
        achievement: true
      },
      priorities: {
        low: true,
        medium: true,
        high: true,
        critical: true
      },
      soundEnabled: true,
      desktopEnabled: false,
      emailEnabled: false,
      maxNotifications: 100,
      autoMarkReadAfter: 30
    };
  }

  private initializeTemplates(): void {
    const templates: NotificationTemplate[] = [
      {
        id: 'mission_complete_success',
        name: 'Mission Complete - Success',
        title: 'Mission Completed Successfully',
        message: 'Mission "{{missionName}}" has been completed successfully! Rewards: {{credits}} credits, {{experience}} XP',
        type: 'success',
        priority: 'high',
        category: 'mission',
        variables: ['missionName', 'credits', 'experience']
      },
      {
        id: 'mission_complete_failure',
        name: 'Mission Complete - Failure',
        title: 'Mission Failed',
        message: 'Mission "{{missionName}}" has failed. Reason: {{reason}}',
        type: 'error',
        priority: 'high',
        category: 'mission',
        variables: ['missionName', 'reason']
      },
      {
        id: 'specialist_hired',
        name: 'Specialist Hired',
        title: 'New Team Member',
        message: '{{specialistName}} has joined your team as a {{specialization}}!',
        type: 'success',
        priority: 'medium',
        category: 'team',
        variables: ['specialistName', 'specialization']
      },
      {
        id: 'level_up',
        name: 'Level Up',
        title: 'Level Up!',
        message: 'Congratulations! You\'ve reached level {{newLevel}}! New features unlocked: {{features}}',
        type: 'success',
        priority: 'high',
        category: 'achievement',
        variables: ['newLevel', 'features']
      },
      {
        id: 'security_threat',
        name: 'Security Threat',
        title: 'Security Alert',
        message: 'Security threat detected: {{threatName}} ({{severity}}). Immediate action required.',
        type: 'error',
        priority: 'critical',
        category: 'security',
        variables: ['threatName', 'severity']
      }
    ];

    templates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * Get notifications with optional limit
   */
  async getNotifications(limit?: number): Promise<Notification[]> {
    const notifications = Array.from(this.notifications.values())
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    return limit ? notifications.slice(0, limit) : notifications;
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(id: string): Promise<Notification | null> {
    return this.notifications.get(id) || null;
  }

  /**
   * Create new notification
   */
  async createNotification(notification: Omit<Notification, 'id' | 'timestamp'>): Promise<Notification> {
    const newNotification: Notification = {
      ...notification,
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    };

    this.notifications.set(newNotification.id, newNotification);
    return newNotification;
  }

  /**
   * Update notification
   */
  async updateNotification(id: string, updates: Partial<Notification>): Promise<void> {
    const notification = this.notifications.get(id);
    if (notification) {
      this.notifications.set(id, { ...notification, ...updates });
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(id: string): Promise<void> {
    this.notifications.delete(id);
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    for (const [id, notification] of this.notifications) {
      this.notifications.set(id, { ...notification, read: true });
    }
  }

  /**
   * Dismiss all notifications
   */
  async dismissAll(): Promise<void> {
    for (const [id, notification] of this.notifications) {
      this.notifications.set(id, { ...notification, dismissed: true });
    }
  }

  /**
   * Delete expired notifications
   */
  async deleteExpired(): Promise<void> {
    const now = new Date();
    const toDelete: string[] = [];

    for (const [id, notification] of this.notifications) {
      if (notification.expiresAt && new Date(notification.expiresAt) < now) {
        toDelete.push(id);
      }
    }

    toDelete.forEach(id => this.notifications.delete(id));
  }

  /**
   * Get unread notifications
   */
  async getUnreadNotifications(): Promise<Notification[]> {
    return Array.from(this.notifications.values())
      .filter(n => !n.read)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Get notifications by category
   */
  async getNotificationsByCategory(category: NotificationCategory): Promise<Notification[]> {
    return Array.from(this.notifications.values())
      .filter(n => n.category === category)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Get notifications by type
   */
  async getNotificationsByType(type: NotificationType): Promise<Notification[]> {
    return Array.from(this.notifications.values())
      .filter(n => n.type === type)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  /**
   * Get notification settings
   */
  async getSettings(): Promise<NotificationSettings> {
    return { ...this.settings };
  }

  /**
   * Update notification settings
   */
  async updateSettings(updates: Partial<NotificationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...updates };
  }

  /**
   * Get notification templates
   */
  async getTemplates(): Promise<NotificationTemplate[]> {
    return Array.from(this.templates.values());
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string): Promise<NotificationTemplate | null> {
    return this.templates.get(id) || null;
  }
}
