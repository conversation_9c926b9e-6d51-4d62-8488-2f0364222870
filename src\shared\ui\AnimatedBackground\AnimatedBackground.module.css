.animatedBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  object-fit: cover;
  pointer-events: none;
  border-radius: inherit;
}

/* Ensure smooth animations */
.animatedBackground polygon[data-animated="true"] {
  transition: none; /* Let JS handle the animation */
}

/* Scale to fit within device container */
.animatedBackground {
  transform: scale(0.5);
  transform-origin: center center;
}

/* Responsive scaling for device container */
@media (max-width: 1440px) {
  .animatedBackground {
    transform: scale(0.45);
  }
}

@media (max-width: 1024px) {
  .animatedBackground {
    transform: scale(0.4);
  }
}

@media (max-width: 768px) {
  .animatedBackground {
    transform: scale(0.35);
  }
}
