.animatedBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
  overflow: hidden;
}

/* Ensure smooth animations */
.animatedBackground polygon[data-animated="true"] {
  transition: none; /* Let JS handle the animation */
}

/* Full coverage for app shell background */
.animatedBackground {
  /* Remove scaling to fill the entire app shell */
  transform: none;
  transform-origin: center center;
}

/* Polygon hover effects for subtle interactivity */
.animatedBackground polygon[data-animated="true"]:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease-in-out;
}

/* Responsive adjustments for different screen sizes */
@media (max-width: 1440px) {
  .animatedBackground {
    /* Maintain full coverage */
    transform: none;
  }
}

@media (max-width: 1024px) {
  .animatedBackground {
    /* Maintain full coverage */
    transform: none;
  }
}

@media (max-width: 768px) {
  .animatedBackground {
    /* Maintain full coverage */
    transform: none;
  }
}

/* Add subtle animation to the entire background */
.animatedBackground {
  animation: backgroundPulse 20s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    filter: brightness(1) saturate(1);
  }
  50% {
    filter: brightness(1.05) saturate(1.1);
  }
}

/* Enhanced visual effects for interconnected polygons */
.animatedBackground polygon[data-animated="true"] {
  /* Subtle stroke to emphasize edges */
  stroke: rgba(0, 179, 164, 0.1);
  stroke-width: 0.5;

  /* Smooth transitions for dynamic effects */
  transition: stroke-opacity 0.3s ease-in-out;
}

/* Enhance connection visibility on hover */
.animatedBackground:hover polygon[data-animated="true"] {
  stroke-opacity: 0.3;
}

/* Responsive polygon stroke adjustments */
@media (max-width: 1024px) {
  .animatedBackground polygon[data-animated="true"] {
    stroke-width: 0.3;
  }
}

@media (max-width: 768px) {
  .animatedBackground polygon[data-animated="true"] {
    stroke-width: 0.2;
  }
}

/* Ensure the background integrates well with glassmorphism */
.animatedBackground {
  mix-blend-mode: multiply;
  opacity: 0.9;
}
