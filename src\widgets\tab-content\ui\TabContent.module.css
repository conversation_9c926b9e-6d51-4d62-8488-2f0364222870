.contentContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* padding: var(--space-sm); */
  box-sizing: border-box;
}

.tabContent {
  display: grid;
  gap: var(--space-md);
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* Tab styling */
.tabPane {
  overflow: auto;
  background: rgba(7, 110, 136, 0.15);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-2xl);
  border: 1px solid rgba(0, 179, 164, 0.2);
  transition: all 0.3s ease;
  position: relative;
  box-sizing: border-box;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tabPane::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(
    135deg,
    rgba(0, 179, 164, 0.05) 0%,
    transparent 50%,
    rgba(7, 110, 136, 0.05) 100%
  );
  border-radius: inherit;
  z-index: 1;
}

/* All tabs are visible in their layout positions */
.tabPane {
  display: block;
}

/* Single tab layout */
.single {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.single .tabPane.fullTab {
  grid-column: 1;
  grid-row: 1;
}

/* Split layout (two tabs side by side) */
.split {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr;
}

.split .tabPane.leftSplitTab {
  grid-column: 1;
  grid-row: 1;
}

.split .tabPane.rightSplitTab {
  grid-column: 2;
  grid-row: 1;
}

/* Triple layout (main tab + two stacked tabs) */
.triple {
  grid-template-columns: 1.5fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.triple .tabPane.mainTripleTab {
  grid-column: 1;
  grid-row: 1 / 3;
}

.triple .tabPane.topStackedTab {
  grid-column: 2;
  grid-row: 1;
}

.triple .tabPane.bottomStackedTab {
  grid-column: 2;
  grid-row: 2;
}

/* Grid layout (2x2 grid) */
.grid {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.grid .tabPane.topLeftGridTab {
  grid-column: 1;
  grid-row: 1;
}

.grid .tabPane.topRightGridTab {
  grid-column: 2;
  grid-row: 1;
}

.grid .tabPane.bottomLeftGridTab {
  grid-column: 1;
  grid-row: 2;
}

.grid .tabPane.bottomRightGridTab {
  grid-column: 2;
  grid-row: 2;
}

/* Scrollbar styling */
.tabPane::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tabPane::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.tabPane::-webkit-scrollbar-thumb {
  background: rgba(0, 179, 164, 0.5);
  border-radius: 4px;
}

.tabPane::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 179, 164, 0.8);
}

/* Tab transition effects */
.tabPane {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for better interactivity */
.tabPane:hover {
  border-color: rgba(0, 179, 164, 0.4);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(0, 179, 164, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}
