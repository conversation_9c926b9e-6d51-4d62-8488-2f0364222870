/**
 * Notification Service - Core business logic for notification operations
 */

import { INotificationService, NotificationRepository } from './interfaces';
import {
  Notification,
  NotificationSettings,
  NotificationStats,
  NotificationType,
  NotificationPriority,
  NotificationCategory
} from './types';

export class NotificationService implements INotificationService {
  private subscribers: Array<(notification: Notification) => void> = [];

  constructor(private notificationRepository: NotificationRepository) {}

  /**
   * Create a new notification
   */
  async createNotification(
    title: string,
    message: string,
    type: NotificationType,
    options?: {
      priority?: NotificationPriority;
      category?: NotificationCategory;
      actionUrl?: string;
      actionText?: string;
      expiresIn?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<Notification> {
    const settings = await this.notificationRepository.getSettings();
    
    // Check if notifications are enabled for this category
    const category = options?.category || 'system';
    if (!settings.enabled || !settings.categories[category]) {
      throw new Error('Notifications disabled for this category');
    }

    // Check priority settings
    const priority = options?.priority || 'medium';
    if (!settings.priorities[priority]) {
      throw new Error('Notifications disabled for this priority level');
    }

    const expiresAt = options?.expiresIn 
      ? new Date(Date.now() + options.expiresIn * 1000).toISOString()
      : undefined;

    const notification = await this.notificationRepository.createNotification({
      title,
      message,
      type,
      priority,
      category,
      read: false,
      dismissed: false,
      actionUrl: options?.actionUrl,
      actionText: options?.actionText,
      expiresAt,
      metadata: options?.metadata
    });

    // Notify subscribers
    this.notifySubscribers(notification);

    // Auto-cleanup if max notifications exceeded
    await this.enforceMaxNotifications();

    return notification;
  }

  /**
   * Create notification from template
   */
  async createFromTemplate(
    templateId: string,
    variables: Record<string, string>
  ): Promise<Notification> {
    const template = await this.notificationRepository.getTemplateById(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // Replace variables in title and message
    let title = template.title;
    let message = template.message;

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      title = title.replace(new RegExp(placeholder, 'g'), value);
      message = message.replace(new RegExp(placeholder, 'g'), value);
    }

    return this.createNotification(title, message, template.type, {
      priority: template.priority,
      category: template.category
    });
  }

  /**
   * Get notifications with optional limit
   */
  async getNotifications(limit?: number): Promise<Notification[]> {
    return this.notificationRepository.getNotifications(limit);
  }

  /**
   * Get unread notifications
   */
  async getUnreadNotifications(): Promise<Notification[]> {
    return this.notificationRepository.getUnreadNotifications();
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    await this.notificationRepository.updateNotification(notificationId, {
      read: true
    });
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    await this.notificationRepository.markAllAsRead();
  }

  /**
   * Dismiss a notification
   */
  async dismissNotification(notificationId: string): Promise<void> {
    await this.notificationRepository.updateNotification(notificationId, {
      dismissed: true
    });
  }

  /**
   * Delete a notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    await this.notificationRepository.deleteNotification(notificationId);
  }

  /**
   * Get notifications by category
   */
  async getNotificationsByCategory(category: NotificationCategory): Promise<Notification[]> {
    return this.notificationRepository.getNotificationsByCategory(category);
  }

  /**
   * Get notifications by priority
   */
  async getNotificationsByPriority(priority: NotificationPriority): Promise<Notification[]> {
    const allNotifications = await this.notificationRepository.getNotifications();
    return allNotifications.filter(n => n.priority === priority);
  }

  /**
   * Search notifications
   */
  async searchNotifications(query: string): Promise<Notification[]> {
    const allNotifications = await this.notificationRepository.getNotifications();
    const searchLower = query.toLowerCase();
    
    return allNotifications.filter(notification =>
      notification.title.toLowerCase().includes(searchLower) ||
      notification.message.toLowerCase().includes(searchLower)
    );
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(): Promise<NotificationStats> {
    const notifications = await this.notificationRepository.getNotifications();
    
    const stats: NotificationStats = {
      total: notifications.length,
      unread: notifications.filter(n => !n.read).length,
      byType: {
        info: 0,
        success: 0,
        warning: 0,
        error: 0
      },
      byCategory: {
        mission: 0,
        team: 0,
        market: 0,
        security: 0,
        system: 0,
        achievement: 0
      },
      byPriority: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
      }
    };

    notifications.forEach(notification => {
      stats.byType[notification.type]++;
      stats.byCategory[notification.category]++;
      stats.byPriority[notification.priority]++;
    });

    return stats;
  }

  /**
   * Get notification settings
   */
  async getSettings(): Promise<NotificationSettings> {
    return this.notificationRepository.getSettings();
  }

  /**
   * Update notification settings
   */
  async updateSettings(settings: Partial<NotificationSettings>): Promise<void> {
    await this.notificationRepository.updateSettings(settings);
  }

  /**
   * System notification: Mission complete
   */
  async notifyMissionComplete(missionId: string, success: boolean): Promise<void> {
    const type = success ? 'success' : 'warning';
    const title = success ? 'Mission Completed' : 'Mission Failed';
    const message = success 
      ? `Mission ${missionId} completed successfully!`
      : `Mission ${missionId} failed. Review the details.`;

    await this.createNotification(title, message, type, {
      category: 'mission',
      priority: 'high',
      metadata: { missionId, success }
    });
  }

  /**
   * System notification: Specialist hired
   */
  async notifySpecialistHired(specialistName: string): Promise<void> {
    await this.createNotification(
      'Specialist Hired',
      `${specialistName} has joined your team!`,
      'success',
      {
        category: 'team',
        priority: 'medium',
        metadata: { specialistName }
      }
    );
  }

  /**
   * System notification: Security threat
   */
  async notifySecurityThreat(threatName: string, severity: string): Promise<void> {
    const priority = severity === 'critical' ? 'critical' : 'high';
    
    await this.createNotification(
      'Security Threat Detected',
      `${threatName} - Severity: ${severity}`,
      'error',
      {
        category: 'security',
        priority,
        metadata: { threatName, severity }
      }
    );
  }

  /**
   * System notification: Market opportunity
   */
  async notifyMarketOpportunity(itemName: string): Promise<void> {
    await this.createNotification(
      'Market Opportunity',
      `${itemName} is now available in the marketplace!`,
      'info',
      {
        category: 'market',
        priority: 'medium',
        metadata: { itemName }
      }
    );
  }

  /**
   * System notification: Achievement unlocked
   */
  async notifyAchievementUnlocked(achievementName: string): Promise<void> {
    await this.createNotification(
      'Achievement Unlocked!',
      `You've earned the "${achievementName}" achievement!`,
      'success',
      {
        category: 'achievement',
        priority: 'high',
        metadata: { achievementName }
      }
    );
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications(): Promise<void> {
    await this.notificationRepository.deleteExpired();
  }

  /**
   * Subscribe to real-time notifications
   */
  subscribeToNotifications(callback: (notification: Notification) => void): () => void {
    this.subscribers.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * Notify all subscribers of new notification
   */
  private notifySubscribers(notification: Notification): void {
    this.subscribers.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error in notification subscriber:', error);
      }
    });
  }

  /**
   * Enforce maximum notification limit
   */
  private async enforceMaxNotifications(): Promise<void> {
    const settings = await this.notificationRepository.getSettings();
    const notifications = await this.notificationRepository.getNotifications();
    
    if (notifications.length > settings.maxNotifications) {
      // Delete oldest notifications
      const toDelete = notifications
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        .slice(0, notifications.length - settings.maxNotifications);
      
      for (const notification of toDelete) {
        await this.notificationRepository.deleteNotification(notification.id);
      }
    }
  }
}
