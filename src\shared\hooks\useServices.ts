/**
 * React hooks for accessing core services
 * These hooks provide a clean interface between React components and business logic
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import {
  getMarketService,
  getUserService,
  getSecurityService,
  getNotificationService,
  getEventBus
} from '@/core/container/ServiceRegistry';
import { Notification } from '@/core/notification/types';
import { SecurityThreat } from '@/core/security/types';
import { UserProfile } from '@/core/user/types';
import { DomainEvent } from '@/core/events/types';

/**
 * Hook for accessing the market service
 */
export function useMarketService() {
  const service = getMarketService();
  
  return {
    searchItems: useCallback(async (query: string, filters?: any) => {
      return service.searchForItems(query, filters);
    }, [service]),
    
    purchaseItem: useCallback(async (itemId: string) => {
      return service.purchaseItem(itemId);
    }, [service]),
    
    sellItem: useCallback(async (itemId: string) => {
      return service.sellItem(itemId);
    }, [service]),
    
    checkRequirements: useCallback(async (itemId: string) => {
      return service.checkItemRequirements(itemId);
    }, [service]),
    
    getSpecialOffers: useCallback(async () => {
      return service.getSpecialOffers();
    }, [service])
  };
}

/**
 * Hook for accessing the user service
 */
export function useUserService() {
  const service = getUserService();
  
  return {
    getCurrentUser: useCallback(async () => {
      return service.getCurrentUser();
    }, [service]),
    
    addExperience: useCallback(async (amount: number) => {
      return service.addExperience(amount);
    }, [service]),
    
    addCredits: useCallback(async (amount: number) => {
      return service.addCredits(amount);
    }, [service]),
    
    spendCredits: useCallback(async (amount: number) => {
      return service.spendCredits(amount);
    }, [service]),
    
    checkAchievements: useCallback(async () => {
      return service.checkAchievements();
    }, [service]),
    
    updateStats: useCallback(async (stats: any) => {
      return service.updateStats(stats);
    }, [service])
  };
}

/**
 * Hook for accessing the security service
 */
export function useSecurityService() {
  const service = getSecurityService();
  
  return {
    scanForThreats: useCallback(async () => {
      return service.scanForThreats();
    }, [service]),
    
    getActiveThreats: useCallback(async () => {
      return service.getActiveThreats();
    }, [service]),
    
    mitigateThreat: useCallback(async (threatId: string) => {
      return service.mitigateThreat(threatId);
    }, [service]),
    
    activateCountermeasure: useCallback(async (countermeasureId: string) => {
      return service.activateCountermeasure(countermeasureId);
    }, [service]),
    
    getSecurityMetrics: useCallback(async () => {
      return service.getSecurityMetrics();
    }, [service]),
    
    performAssessment: useCallback(async () => {
      return service.performSecurityAssessment();
    }, [service])
  };
}

/**
 * Hook for accessing the notification service
 */
export function useNotificationService() {
  const service = getNotificationService();
  
  return {
    createNotification: useCallback(async (title: string, message: string, type: any, options?: any) => {
      return service.createNotification(title, message, type, options);
    }, [service]),
    
    getNotifications: useCallback(async (limit?: number) => {
      return service.getNotifications(limit);
    }, [service]),
    
    getUnreadNotifications: useCallback(async () => {
      return service.getUnreadNotifications();
    }, [service]),
    
    markAsRead: useCallback(async (notificationId: string) => {
      return service.markAsRead(notificationId);
    }, [service]),
    
    markAllAsRead: useCallback(async () => {
      return service.markAllAsRead();
    }, [service]),
    
    dismissNotification: useCallback(async (notificationId: string) => {
      return service.dismissNotification(notificationId);
    }, [service])
  };
}

/**
 * Hook for real-time notifications
 */
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const service = getNotificationService();
  const subscriptionRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Load initial notifications
    const loadNotifications = async () => {
      try {
        const allNotifications = await service.getNotifications(50);
        const unread = await service.getUnreadNotifications();
        
        setNotifications(allNotifications);
        setUnreadCount(unread.length);
      } catch (error) {
        console.error('Failed to load notifications:', error);
      }
    };

    loadNotifications();

    // Subscribe to new notifications
    subscriptionRef.current = service.subscribeToNotifications((notification) => {
      setNotifications(prev => [notification, ...prev]);
      if (!notification.read) {
        setUnreadCount(prev => prev + 1);
      }
    });

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current();
      }
    };
  }, [service]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await service.markAsRead(notificationId);
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, [service]);

  const markAllAsRead = useCallback(async () => {
    try {
      await service.markAllAsRead();
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, [service]);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead
  };
}

/**
 * Hook for real-time security monitoring
 */
export function useSecurityMonitoring() {
  const [threats, setThreats] = useState<SecurityThreat[]>([]);
  const [securityLevel, setSecurityLevel] = useState(0);
  const service = getSecurityService();
  const eventBus = getEventBus();

  useEffect(() => {
    // Load initial security data
    const loadSecurityData = async () => {
      try {
        const activeThreats = await service.getActiveThreats();
        const currentLevel = await service.getCurrentSecurityLevel();
        
        setThreats(activeThreats);
        setSecurityLevel(currentLevel);
      } catch (error) {
        console.error('Failed to load security data:', error);
      }
    };

    loadSecurityData();

    // Subscribe to security events
    const unsubscribe = eventBus.subscribe('security.threat_detected', async (event: any) => {
      const updatedThreats = await service.getActiveThreats();
      setThreats(updatedThreats);
    });

    return unsubscribe;
  }, [service, eventBus]);

  return {
    threats,
    securityLevel,
    refreshData: useCallback(async () => {
      const activeThreats = await service.getActiveThreats();
      const currentLevel = await service.getCurrentSecurityLevel();
      setThreats(activeThreats);
      setSecurityLevel(currentLevel);
    }, [service])
  };
}

/**
 * Hook for user profile data
 */
export function useUserProfile() {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const service = getUserService();
  const eventBus = getEventBus();

  useEffect(() => {
    // Load user profile
    const loadUser = async () => {
      try {
        const userProfile = await service.getCurrentUser();
        setUser(userProfile);
      } catch (error) {
        console.error('Failed to load user profile:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUser();

    // Subscribe to user events
    const unsubscribeResources = eventBus.subscribe('user.resources_changed', async () => {
      const updatedUser = await service.getCurrentUser();
      setUser(updatedUser);
    });

    const unsubscribeLevelUp = eventBus.subscribe('user.leveled_up', async () => {
      const updatedUser = await service.getCurrentUser();
      setUser(updatedUser);
    });

    return () => {
      unsubscribeResources();
      unsubscribeLevelUp();
    };
  }, [service, eventBus]);

  return {
    user,
    loading,
    refreshUser: useCallback(async () => {
      const userProfile = await service.getCurrentUser();
      setUser(userProfile);
    }, [service])
  };
}

/**
 * Hook for subscribing to domain events
 */
export function useEventSubscription(eventType: string, handler: (event: DomainEvent) => void) {
  const eventBus = getEventBus();

  useEffect(() => {
    const unsubscribe = eventBus.subscribe(eventType, handler);
    return unsubscribe;
  }, [eventType, handler, eventBus]);
}
