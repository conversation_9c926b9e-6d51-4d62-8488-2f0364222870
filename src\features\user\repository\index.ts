/**
 * Mock User Repository implementation for local development
 */

import { UserRepository } from '@/core/user/interfaces';
import { UserProfile, Achievement, UserPreferences, UserResource } from '@/core/user/types';
import { mockUserProfile } from '@/shared/data/mock';

/**
 * Mock implementation of User Repository
 */
export class MockUserRepository implements UserRepository {
  private users: Record<string, UserProfile> = {};

  constructor() {
    // Initialize with mock user data
    this.users[mockUserProfile.id] = { ...mockUserProfile };
  }

  /**
   * Get user profile by ID
   */
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    return this.users[userId] || null;
  }

  /**
   * Update user profile
   */
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    if (this.users[userId]) {
      this.users[userId] = { ...this.users[userId], ...updates };
    }
  }

  /**
   * Create new user profile
   */
  async createUserProfile(profile: Omit<UserProfile, 'id'>): Promise<UserProfile> {
    const newProfile: UserProfile = {
      ...profile,
      id: `user-${Date.now()}`
    };
    
    this.users[newProfile.id] = newProfile;
    return newProfile;
  }

  /**
   * Get user resources
   */
  async getUserResources(userId: string): Promise<UserResource[]> {
    const user = this.users[userId];
    if (!user) return [];

    return [
      {
        type: 'credits',
        amount: user.credits,
        lastUpdated: new Date().toISOString()
      },
      {
        type: 'experience',
        amount: user.experience,
        lastUpdated: new Date().toISOString()
      },
      {
        type: 'reputation',
        amount: user.reputation,
        lastUpdated: new Date().toISOString()
      }
    ];
  }

  /**
   * Update user resource
   */
  async updateUserResource(userId: string, resource: UserResource): Promise<void> {
    const user = this.users[userId];
    if (!user) return;

    switch (resource.type) {
      case 'credits':
        user.credits = resource.amount;
        break;
      case 'experience':
        user.experience = resource.amount;
        break;
      case 'reputation':
        user.reputation = resource.amount;
        break;
    }
  }

  /**
   * Get user achievements
   */
  async getUserAchievements(userId: string): Promise<Achievement[]> {
    const user = this.users[userId];
    return user?.achievements || [];
  }

  /**
   * Unlock achievement for user
   */
  async unlockAchievement(userId: string, achievementId: string): Promise<void> {
    const user = this.users[userId];
    if (!user) return;

    // Check if achievement is already unlocked
    const hasAchievement = user.achievements.some(ach => ach.id === achievementId);
    if (hasAchievement) return;

    // Mock achievement data - in real app this would come from an achievements repository
    const mockAchievements: Record<string, Omit<Achievement, 'unlockedAt'>> = {
      'ach-1': {
        id: 'ach-1',
        name: 'First Steps',
        description: 'Complete your first mission',
        icon: '🎯',
        rarity: 'common'
      },
      'ach-2': {
        id: 'ach-2',
        name: 'Team Builder',
        description: 'Hire your first specialist',
        icon: '👥',
        rarity: 'common'
      },
      'ach-3': {
        id: 'ach-3',
        name: 'Ghost in the Machine',
        description: 'Complete a mission without being detected',
        icon: '👻',
        rarity: 'rare'
      },
      'ach-4': {
        id: 'ach-4',
        name: 'Master Infiltrator',
        description: 'Successfully infiltrate 10 high-security targets',
        icon: '🔓',
        rarity: 'epic'
      },
      'ach-5': {
        id: 'ach-5',
        name: 'Millionaire',
        description: 'Accumulate 1,000,000 credits',
        icon: '💰',
        rarity: 'rare'
      },
      'level-10': {
        id: 'level-10',
        name: 'Veteran Operator',
        description: 'Reach level 10',
        icon: '⭐',
        rarity: 'uncommon'
      },
      'level-25': {
        id: 'level-25',
        name: 'Elite Hacker',
        description: 'Reach level 25',
        icon: '💎',
        rarity: 'epic'
      }
    };

    const achievementTemplate = mockAchievements[achievementId];
    if (achievementTemplate) {
      const newAchievement: Achievement = {
        ...achievementTemplate,
        unlockedAt: new Date().toISOString()
      };

      user.achievements.push(newAchievement);
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    const user = this.users[userId];
    return user?.preferences || {
      theme: 'dark',
      notifications: {
        missions: true,
        market: true,
        team: true,
        security: true
      },
      privacy: {
        showOnlineStatus: true,
        allowDirectMessages: false,
        shareStatistics: true
      },
      interface: {
        compactMode: false,
        showTooltips: true,
        animationsEnabled: true
      }
    };
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<void> {
    const user = this.users[userId];
    if (!user) return;

    user.preferences = { ...user.preferences, ...preferences };
  }
}
