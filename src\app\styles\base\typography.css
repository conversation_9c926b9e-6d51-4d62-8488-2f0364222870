/* Modern Typography */

body {
  font-family: var(--font-main);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  margin: 0 0 0.75em 0;
  letter-spacing: -0.025em;
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-primary);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: 600;
  color: var(--text-primary);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: 500;
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: 500;
}

h6 {
  font-size: var(--font-size-md);
  font-weight: 500;
}

p {
  margin: 0 0 1em 0;
}

/* Modern Links */
a {
  color: var(--text-accent);
  text-decoration: none;
  transition: color var(--transition-fast);
  position: relative;
  font-weight: 500;
}

a:hover {
  color: var(--primary-300);
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transform-origin: right;
  transition: transform var(--transition-normal);
  border-radius: var(--border-radius-full);
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Monospace text */
code, pre, .monospace {
  font-family: var(--font-mono);
  font-size: 0.875em;
  font-weight: 500;
}

/* Code blocks */
.code-block {
  font-family: var(--font-mono);
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
  padding: 0.5em 0.75em;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-subtle);
  font-size: 0.875em;
}
