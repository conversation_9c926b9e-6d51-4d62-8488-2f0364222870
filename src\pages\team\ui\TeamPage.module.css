.teamPage {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
}

.teamHeader {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-md);
  border-bottom: 1px solid rgba(0, 179, 164, 0.2);
}

.teamTitle {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.5px;
}

.welcomeMessage {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 400;
}

.teamContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.filters {
  display: flex;
  gap: 0.5rem;
}

.filterButton {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.filterButton:hover {
  background-color: var(--bg-hover);
}

.filterButton.active {
  background-color: var(--bg-accent);
  color: var(--text-on-accent);
  border-color: var(--accent-color);
}

.content {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.tabsContainer {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.tabButton {
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-hover);
  border: 1px solid rgba(0, 179, 164, 0.2);
  border-radius: var(--border-radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.tabButton:hover {
  background: rgba(0, 179, 164, 0.1);
  color: var(--text-primary);
  border-color: var(--accent-primary);
}

.tabButton.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--bg-primary);
}

.noSpecialistSelected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.noSpecialistSelected p {
  margin-bottom: 1rem;
}

.noSpecialistSelected button {
  background-color: var(--bg-accent);
  color: var(--text-on-accent);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

.listGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  padding: 0.5rem;
}

.specialistCard {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.specialistCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: var(--accent-color);
}

.cardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bg-accent);
  color: var(--text-on-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 0.75rem;
}

.specialistInfo {
  flex: 1;
}

.specialistName {
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: var(--text-primary);
}

.specialization {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  text-transform: uppercase;
  font-weight: 600;
}

.statusAvailable {
  background-color: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.statusHired {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.statusOnMission {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.statusTraining {
  background-color: rgba(241, 196, 15, 0.2);
  color: #f1c40f;
}

.skills {
  margin: 0.75rem 0;
}

.skillRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
}

.skillName {
  color: var(--text-secondary);
}

.skillValue {
  color: var(--text-primary);
  font-weight: 600;
}

.attributes {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 0.75rem 0;
}

.attribute {
  background-color: var(--bg-tertiary);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
}

.attributeLabel {
  color: var(--text-secondary);
  margin-right: 0.25rem;
}

.attributeValue {
  color: var(--text-primary);
  font-weight: 600;
}

.cardFooter {
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost {
  font-weight: 600;
  color: var(--accent-color);
}

.hireButton {
  background-color: var(--bg-accent);
  color: var(--text-on-accent);
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
}

.hireButton:hover {
  background-color: var(--accent-color-dark);
}

.fireButton {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  border: 1px solid #e74c3c;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
}

.fireButton:hover {
  background-color: rgba(231, 76, 60, 0.3);
}
