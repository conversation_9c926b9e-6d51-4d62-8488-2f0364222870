/**
 * Application - Main application orchestrator
 * This class will work identically on frontend and backend
 */

import { registerServices, getEventBus, getNotificationService, getSecurityService } from './container/ServiceRegistry';
import { IEventBus } from './events/EventBus';
import { INotificationService } from './notification/interfaces';
import { ISecurityService } from './security/interfaces';
import { gameTimeSystem } from './time/TimeFlowSystem';

export interface ApplicationConfig {
  environment: 'development' | 'production' | 'test';
  enableNotifications: boolean;
  enableSecurityMonitoring: boolean;
  enableEventLogging: boolean;
  autoCleanupInterval: number; // minutes
}

export class Application {
  private eventBus: IEventBus;
  private notificationService: INotificationService;
  private securityService: ISecurityService;
  private cleanupInterval?: NodeJS.Timeout;
  private isInitialized = false;

  constructor(private config: ApplicationConfig) {}

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Application is already initialized');
    }

    try {
      // Register all services with dependency injection container
      registerServices();

      // Get core services
      this.eventBus = getEventBus();
      this.notificationService = getNotificationService();
      this.securityService = getSecurityService();

      // Start core services
      await this.startCoreServices();

      // Configure and start game time system
      // 60 ticks in 10 seconds = 1 tick every 166.67ms
      // 60 ticks = 1 hour, so 1 tick = 1 minute
      gameTimeSystem.updateConfig({
        tickDurationMs: 167, // ~166.67ms per tick for 60 ticks in 10 seconds
        ticksPerHour: 60,    // 60 ticks = 1 hour (60 minutes)
        ticksPerDay: 1440,   // 24 hours * 60 ticks = 1440 ticks per day
        paused: false,
        speed: 1
      });
      gameTimeSystem.start();

      // Setup periodic cleanup
      this.setupPeriodicCleanup();

      // Setup application-level event handlers
      this.setupApplicationEventHandlers();

      this.isInitialized = true;

      console.log('🚀 Application initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize application:', error);
      throw error;
    }
  }

  /**
   * Shutdown the application gracefully
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Stop periodic cleanup
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      // Stop game time system
      gameTimeSystem.stop();

      // Stop security monitoring
      if (this.config.enableSecurityMonitoring) {
        await this.securityService.stopContinuousMonitoring();
      }

      // Cleanup expired notifications
      await this.notificationService.cleanupExpiredNotifications();

      // Clear event subscriptions
      this.eventBus.unsubscribeAll();

      this.isInitialized = false;

      console.log('✅ Application shutdown completed');
    } catch (error) {
      console.error('❌ Error during application shutdown:', error);
      throw error;
    }
  }

  /**
   * Get application status
   */
  getStatus(): {
    initialized: boolean;
    environment: string;
    services: {
      notifications: boolean;
      security: boolean;
      eventLogging: boolean;
    };
  } {
    return {
      initialized: this.isInitialized,
      environment: this.config.environment,
      services: {
        notifications: this.config.enableNotifications,
        security: this.config.enableSecurityMonitoring,
        eventLogging: this.config.enableEventLogging
      }
    };
  }

  /**
   * Start core application services
   */
  private async startCoreServices(): Promise<void> {
    // Start security monitoring if enabled
    if (this.config.enableSecurityMonitoring) {
      await this.securityService.startContinuousMonitoring();
      console.log('🔒 Security monitoring started');
    }

    // Initialize notification system
    if (this.config.enableNotifications) {
      // Create welcome notification
      await this.notificationService.createNotification(
        'System Ready',
        'Welcome to the Cyber Operations Platform. All systems are operational.',
        'success',
        {
          category: 'system',
          priority: 'medium'
        }
      );
      console.log('🔔 Notification system started');
    }
  }

  /**
   * Setup periodic cleanup tasks
   */
  private setupPeriodicCleanup(): void {
    if (this.config.autoCleanupInterval <= 0) {
      return;
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        // Cleanup expired notifications
        await this.notificationService.cleanupExpiredNotifications();

        // Log cleanup event if enabled
        if (this.config.enableEventLogging) {
          console.log('🧹 Periodic cleanup completed');
        }
      } catch (error) {
        console.error('❌ Error during periodic cleanup:', error);
      }
    }, this.config.autoCleanupInterval * 60 * 1000); // Convert minutes to milliseconds
  }

  /**
   * Setup application-level event handlers
   */
  private setupApplicationEventHandlers(): void {
    if (!this.config.enableEventLogging) {
      return;
    }

    // Log all events in development mode
    if (this.config.environment === 'development') {
      this.eventBus.subscribe('*', (event) => {
        console.log('📡 Event:', event.type, event.data);
      });
    }

    // Handle critical system events
    this.eventBus.subscribe('system.maintenance', async (event: any) => {
      await this.notificationService.createNotification(
        'System Maintenance',
        `Scheduled maintenance: ${event.data.maintenanceType}. Estimated duration: ${event.data.estimatedDuration} minutes.`,
        'warning',
        {
          category: 'system',
          priority: 'high',
          metadata: event.data
        }
      );
    });

    // Handle security incidents
    this.eventBus.subscribe('security.threat_detected', async (event: any) => {
      if (event.data.severity === 'critical') {
        // Initiate incident response for critical threats
        await this.securityService.initiateIncidentResponse(event.data.threatId);
      }
    });
  }
}

// Default application configuration
export const defaultConfig: ApplicationConfig = {
  environment: 'development',
  enableNotifications: true,
  enableSecurityMonitoring: true,
  enableEventLogging: true,
  autoCleanupInterval: 30 // 30 minutes
};

// Global application instance
let appInstance: Application | null = null;

/**
 * Initialize the global application instance
 */
export async function initializeApplication(config: Partial<ApplicationConfig> = {}): Promise<Application> {
  if (appInstance) {
    throw new Error('Application is already initialized');
  }

  const finalConfig = { ...defaultConfig, ...config };
  appInstance = new Application(finalConfig);
  await appInstance.initialize();
  
  return appInstance;
}

/**
 * Get the global application instance
 */
export function getApplication(): Application {
  if (!appInstance) {
    throw new Error('Application not initialized. Call initializeApplication() first.');
  }
  return appInstance;
}

/**
 * Shutdown the global application instance
 */
export async function shutdownApplication(): Promise<void> {
  if (appInstance) {
    await appInstance.shutdown();
    appInstance = null;
  }
}
