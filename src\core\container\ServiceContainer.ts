/**
 * Service Container - Dependency injection container
 * This will work identically on frontend and backend
 */

export interface ServiceDefinition<T = any> {
  factory: (container: ServiceContainer) => T;
  singleton?: boolean;
  dependencies?: string[];
}

export interface IServiceContainer {
  register<T>(name: string, definition: ServiceDefinition<T>): void;
  get<T>(name: string): T;
  has(name: string): boolean;
  resolve<T>(factory: (container: ServiceContainer) => T): T;
}

export class ServiceContainer implements IServiceContainer {
  private services: Map<string, ServiceDefinition> = new Map();
  private instances: Map<string, any> = new Map();
  private resolving: Set<string> = new Set();

  /**
   * Register a service with the container
   */
  register<T>(name: string, definition: ServiceDefinition<T>): void {
    this.services.set(name, definition);
  }

  /**
   * Get a service instance
   */
  get<T>(name: string): T {
    // Check for circular dependencies
    if (this.resolving.has(name)) {
      throw new Error(`Circular dependency detected: ${name}`);
    }

    const definition = this.services.get(name);
    if (!definition) {
      throw new Error(`Service not found: ${name}`);
    }

    // Return existing singleton instance
    if (definition.singleton && this.instances.has(name)) {
      return this.instances.get(name);
    }

    // Mark as resolving to detect circular dependencies
    this.resolving.add(name);

    try {
      // Create new instance
      const instance = definition.factory(this);

      // Cache singleton instances
      if (definition.singleton) {
        this.instances.set(name, instance);
      }

      return instance;
    } finally {
      // Remove from resolving set
      this.resolving.delete(name);
    }
  }

  /**
   * Check if a service is registered
   */
  has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Resolve dependencies for a factory function
   */
  resolve<T>(factory: (container: ServiceContainer) => T): T {
    return factory(this);
  }

  /**
   * Clear all instances (useful for testing)
   */
  clear(): void {
    this.instances.clear();
    this.resolving.clear();
  }

  /**
   * Get all registered service names
   */
  getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }
}

// Service registration helpers
export const singleton = <T>(factory: (container: ServiceContainer) => T): ServiceDefinition<T> => ({
  factory,
  singleton: true
});

export const transient = <T>(factory: (container: ServiceContainer) => T): ServiceDefinition<T> => ({
  factory,
  singleton: false
});

// Global container instance
export const container = new ServiceContainer();
