/**
 * Event Bus - Domain event system for cross-service communication
 * This implementation will work identically on frontend and backend
 */

import { DomainEvent, EventHandler, EventSubscription, GameEvent } from './types';

export interface IEventBus {
  publish<T extends DomainEvent>(event: T): Promise<void>;
  subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): string;
  subscribeOnce<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): string;
  unsubscribe(subscriptionId: string): void;
  unsubscribeAll(eventType?: string): void;
  getSubscriptions(eventType?: string): EventSubscription[];
}

export class EventBus implements IEventBus {
  private subscriptions: Map<string, EventSubscription> = new Map();
  private eventTypeSubscriptions: Map<string, Set<string>> = new Map();

  /**
   * Publish an event to all subscribers
   */
  async publish<T extends DomainEvent>(event: T): Promise<void> {
    const subscriptionIds = this.eventTypeSubscriptions.get(event.type) || new Set();
    
    // Execute all handlers for this event type
    const promises: Promise<void>[] = [];
    
    for (const subscriptionId of subscriptionIds) {
      const subscription = this.subscriptions.get(subscriptionId);
      if (!subscription) continue;

      try {
        const result = subscription.handler(event);
        if (result instanceof Promise) {
          promises.push(result);
        }

        // Remove one-time subscriptions
        if (subscription.once) {
          this.unsubscribe(subscriptionId);
        }
      } catch (error) {
        console.error(`Error in event handler for ${event.type}:`, error);
      }
    }

    // Wait for all async handlers to complete
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * Subscribe to an event type
   */
  subscribe<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): string {
    const subscriptionId = this.generateSubscriptionId();
    
    const subscription: EventSubscription = {
      id: subscriptionId,
      eventType,
      handler: handler as EventHandler,
      once: false
    };

    this.subscriptions.set(subscriptionId, subscription);
    
    // Add to event type index
    if (!this.eventTypeSubscriptions.has(eventType)) {
      this.eventTypeSubscriptions.set(eventType, new Set());
    }
    this.eventTypeSubscriptions.get(eventType)!.add(subscriptionId);

    return subscriptionId;
  }

  /**
   * Subscribe to an event type for one-time execution
   */
  subscribeOnce<T extends DomainEvent>(eventType: string, handler: EventHandler<T>): string {
    const subscriptionId = this.generateSubscriptionId();
    
    const subscription: EventSubscription = {
      id: subscriptionId,
      eventType,
      handler: handler as EventHandler,
      once: true
    };

    this.subscriptions.set(subscriptionId, subscription);
    
    // Add to event type index
    if (!this.eventTypeSubscriptions.has(eventType)) {
      this.eventTypeSubscriptions.set(eventType, new Set());
    }
    this.eventTypeSubscriptions.get(eventType)!.add(subscriptionId);

    return subscriptionId;
  }

  /**
   * Unsubscribe from an event
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return;

    // Remove from main subscriptions
    this.subscriptions.delete(subscriptionId);

    // Remove from event type index
    const eventTypeSet = this.eventTypeSubscriptions.get(subscription.eventType);
    if (eventTypeSet) {
      eventTypeSet.delete(subscriptionId);
      
      // Clean up empty sets
      if (eventTypeSet.size === 0) {
        this.eventTypeSubscriptions.delete(subscription.eventType);
      }
    }
  }

  /**
   * Unsubscribe all handlers for an event type, or all handlers if no type specified
   */
  unsubscribeAll(eventType?: string): void {
    if (eventType) {
      // Unsubscribe all handlers for specific event type
      const subscriptionIds = this.eventTypeSubscriptions.get(eventType);
      if (subscriptionIds) {
        for (const subscriptionId of subscriptionIds) {
          this.subscriptions.delete(subscriptionId);
        }
        this.eventTypeSubscriptions.delete(eventType);
      }
    } else {
      // Unsubscribe all handlers
      this.subscriptions.clear();
      this.eventTypeSubscriptions.clear();
    }
  }

  /**
   * Get all subscriptions, optionally filtered by event type
   */
  getSubscriptions(eventType?: string): EventSubscription[] {
    if (eventType) {
      const subscriptionIds = this.eventTypeSubscriptions.get(eventType) || new Set();
      return Array.from(subscriptionIds)
        .map(id => this.subscriptions.get(id))
        .filter((sub): sub is EventSubscription => sub !== undefined);
    }

    return Array.from(this.subscriptions.values());
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance for the application
export const eventBus = new EventBus();

// Helper functions for common event operations
export const publishEvent = <T extends DomainEvent>(event: T): Promise<void> => {
  return eventBus.publish(event);
};

export const subscribeToEvent = <T extends DomainEvent>(
  eventType: string, 
  handler: EventHandler<T>
): string => {
  return eventBus.subscribe(eventType, handler);
};

export const subscribeOnceToEvent = <T extends DomainEvent>(
  eventType: string, 
  handler: EventHandler<T>
): string => {
  return eventBus.subscribeOnce(eventType, handler);
};

// Event factory functions for type safety
export const createEvent = <T extends DomainEvent>(
  type: T['type'],
  source: string,
  data: T['data']
): T => {
  return {
    id: `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    timestamp: new Date().toISOString(),
    source,
    data,
    version: 1
  } as T;
};
