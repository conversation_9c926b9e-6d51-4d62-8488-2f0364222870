/**
 * Mock dashboard data for development and testing
 */

export interface DashboardEvent {
  id: string;
  title: string;
  time: string;
  type: 'info' | 'success' | 'warning' | 'error';
  description?: string;
}

export interface DashboardStats {
  totalMissions: number;
  activeMissions: number;
  completedMissions: number;
  totalSpecialists: number;
  availableSpecialists: number;
  credits: number;
  reputation: number;
  securityLevel: number;
}

export const mockDashboardEvents: DashboardEvent[] = [
  {
    id: 'event-1',
    title: 'New vulnerability discovered',
    time: '10:23',
    type: 'info',
    description: 'Zero-day exploit found in popular web framework'
  },
  {
    id: 'event-2',
    title: 'Team member completed training',
    time: '09:15',
    type: 'success',
    description: '<PERSON> finished Advanced Network Infiltration course'
  },
  {
    id: 'event-3',
    title: 'Security breach detected',
    time: 'Yesterday',
    type: 'warning',
    description: 'Unusual activity detected on secure server'
  },
  {
    id: 'event-4',
    title: 'Mission completed successfully',
    time: 'Yesterday',
    type: 'success',
    description: 'Corporate espionage mission finished with bonus objectives'
  },
  {
    id: 'event-5',
    title: 'New specialist available',
    time: '2 days ago',
    type: 'info',
    description: 'Elite malware developer seeking employment'
  },
  {
    id: 'event-6',
    title: 'Payment received',
    time: '2 days ago',
    type: 'success',
    description: 'Client payment of $25,000 processed successfully'
  },
  {
    id: 'event-7',
    title: 'Equipment malfunction',
    time: '3 days ago',
    type: 'error',
    description: 'Surveillance equipment failure during active mission'
  },
  {
    id: 'event-8',
    title: 'Market opportunity',
    time: '3 days ago',
    type: 'info',
    description: 'Rare exploit kit available on dark market'
  }
];

export const mockDashboardStats: DashboardStats = {
  totalMissions: 47,
  activeMissions: 3,
  completedMissions: 42,
  totalSpecialists: 12,
  availableSpecialists: 8,
  credits: 125000,
  reputation: 850,
  securityLevel: 7
};

export const mockRecentEvents = mockDashboardEvents.slice(0, 5);

export interface SystemStatus {
  cpu: number;
  memory: number;
  network: number;
  security: number;
}

export const mockSystemStatus: SystemStatus = {
  cpu: 45,
  memory: 62,
  network: 78,
  security: 85
};
