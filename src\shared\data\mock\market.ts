/**
 * Mock marketplace data for development and testing
 */

import { ItemCategory, ItemRarity, ItemLegality, MarketType } from '@/core/market/types';

export interface MarketItem {
  id: string;
  name: string;
  description: string;
  price: number;
  rarity: ItemRarity;
  category?: ItemCategory;
  legality?: ItemLegality;
  marketSource?: MarketType;
  available?: boolean;
  discount?: number;
}

export const mockMarketItems: Record<string, MarketItem[]> = {
  public: [
    {
      id: 'item-pub-1',
      name: 'Basic Firewall',
      description: 'Entry-level protection against common threats. Provides basic network security monitoring.',
      price: 500,
      rarity: 'common',
      category: 'software',
      legality: 'legal',
      marketSource: 'public',
      available: true
    },
    {
      id: 'item-pub-2',
      name: 'Network Scanner',
      description: 'Identifies vulnerabilities in target networks. Essential tool for reconnaissance.',
      price: 750,
      rarity: 'common',
      category: 'software',
      legality: 'legal',
      marketSource: 'public',
      available: true
    },
    {
      id: 'item-pub-3',
      name: 'VPN Service',
      description: 'Anonymous browsing and secure connection tunneling. 1-year subscription.',
      price: 300,
      rarity: 'common',
      category: 'service',
      legality: 'legal',
      marketSource: 'public',
      available: true
    },
    {
      id: 'item-pub-4',
      name: 'Encrypted Storage',
      description: 'Secure cloud storage with military-grade encryption. 1TB capacity.',
      price: 200,
      rarity: 'common',
      category: 'infrastructure',
      legality: 'legal',
      marketSource: 'public',
      available: true
    }
  ],
  gray: [
    {
      id: 'item-gray-1',
      name: 'Advanced Encryption',
      description: 'Military-grade encryption for your data. Bypasses most government surveillance.',
      price: 2000,
      rarity: 'rare',
      category: 'software',
      legality: 'gray',
      marketSource: 'gray',
      available: true
    },
    {
      id: 'item-gray-2',
      name: 'Trace Cleaner',
      description: 'Removes digital footprints after operations. Essential for maintaining anonymity.',
      price: 1500,
      rarity: 'uncommon',
      category: 'software',
      legality: 'gray',
      marketSource: 'gray',
      available: true
    },
    {
      id: 'item-gray-3',
      name: 'Identity Package',
      description: 'Complete false identity with documentation. Includes passport, driver\'s license, and credit history.',
      price: 5000,
      rarity: 'rare',
      category: 'data',
      legality: 'gray',
      marketSource: 'gray',
      available: true
    },
    {
      id: 'item-gray-4',
      name: 'Surveillance Equipment',
      description: 'Professional-grade surveillance kit. Includes cameras, microphones, and tracking devices.',
      price: 3500,
      rarity: 'uncommon',
      category: 'hardware',
      legality: 'gray',
      marketSource: 'gray',
      available: true
    }
  ],
  dark: [
    {
      id: 'item-dark-1',
      name: 'Zero-Day Exploit',
      description: 'Undisclosed vulnerability with no available patches. Targets major operating systems.',
      price: 15000,
      rarity: 'legendary',
      category: 'software',
      legality: 'illegal',
      marketSource: 'dark',
      available: true
    },
    {
      id: 'item-dark-2',
      name: 'Backdoor Implant',
      description: 'Persistent access to compromised systems. Undetectable by most antivirus software.',
      price: 8500,
      rarity: 'rare',
      category: 'software',
      legality: 'illegal',
      marketSource: 'dark',
      available: true
    },
    {
      id: 'item-dark-3',
      name: 'Stolen Database',
      description: 'Complete customer database from major corporation. Contains personal and financial information.',
      price: 25000,
      rarity: 'legendary',
      category: 'data',
      legality: 'illegal',
      marketSource: 'dark',
      available: true,
      discount: 15
    },
    {
      id: 'item-dark-4',
      name: 'Botnet Access',
      description: 'Control over 10,000+ compromised machines. Perfect for DDoS attacks or cryptocurrency mining.',
      price: 12000,
      rarity: 'rare',
      category: 'infrastructure',
      legality: 'illegal',
      marketSource: 'dark',
      available: true
    },
    {
      id: 'item-dark-5',
      name: 'Government Credentials',
      description: 'Stolen government employee credentials with high-level access. Extremely dangerous to use.',
      price: 50000,
      rarity: 'legendary',
      category: 'data',
      legality: 'illegal',
      marketSource: 'dark',
      available: false // Out of stock
    }
  ]
};

export const mockMarketCategories = [
  { id: 'public', name: 'Public Market' },
  { id: 'gray', name: 'Gray Market' },
  { id: 'dark', name: 'Dark Market' }
];

// Flatten all items for search functionality
export const mockAllMarketItems = Object.values(mockMarketItems).flat();
