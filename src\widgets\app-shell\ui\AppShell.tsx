import React from 'react';
import TabBar from '../../tab-bar/ui/TabBar';
import TabContent from '../../tab-content/ui/TabContent';
import TopBar from '../../top-bar/ui/TopBar';
import BottomBar from '../../bottom-bar/ui/BottomBar';
import { NotificationList } from '../../notifications';
import { AnimatedBackground } from '../../../shared/ui/AnimatedBackground';
import { BackgroundRotator } from '../../../shared/ui/BackgroundRotator';
import styles from './AppShell.module.css';

// Import background images
import bg1 from '../../../shared/assets/images/bg1.png';
import bg2 from '../../../shared/assets/images/bg2.png';

const AppShell: React.FC = () => {
  const backgroundImages = [bg1, bg2];

  return (
    <>
      {/* Background layers */}
      <BackgroundRotator images={backgroundImages} interval={10000} />

      {/* Device-like app shell */}
      <div className={styles.appShell}>
        {/* Animated background inside the device */}
        <AnimatedBackground />

        {/* Top area with configurable widgets, system info, time, etc. */}
        <TopBar />

        {/* Main application area */}
        <div className={styles.mainArea}>
          {/* Active tab content */}
          <div className={styles.contentArea}>
            <TabContent />
          </div>
        </div>

        {/* Bottom area with app launching */}
        <BottomBar />

        {/* Notifications */}
        <NotificationList />
      </div>
    </>
  );
};

export default AppShell;
