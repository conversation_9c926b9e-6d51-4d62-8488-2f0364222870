/**
 * User Service - Core business logic for user/player operations
 */

import { IUserService, UserRepository } from './interfaces';
import { UserProfile, Achievement, UserPreferences, LevelRequirement } from './types';

export class UserService implements IUserService {
  private currentUserId = 'user-1'; // Mock current user ID

  constructor(private userRepository: UserRepository) {}

  /**
   * Get the current user's profile
   */
  async getCurrentUser(): Promise<UserProfile | null> {
    return this.userRepository.getUserProfile(this.currentUserId);
  }

  /**
   * Update the current user's profile
   */
  async updateProfile(updates: Partial<UserProfile>): Promise<void> {
    await this.userRepository.updateUserProfile(this.currentUserId, updates);
  }

  /**
   * Add experience points and check for level up
   */
  async addExperience(amount: number): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) return;

    const newExperience = user.experience + amount;
    const newLevel = this.calculateLevel(newExperience);
    const experienceToNext = this.calculateExperienceToNext(newLevel, newExperience);

    await this.updateProfile({
      experience: newExperience,
      level: newLevel,
      experienceToNext
    });

    // Check for level up achievements
    if (newLevel > user.level) {
      await this.checkLevelUpAchievements(newLevel);
    }
  }

  /**
   * Check if user leveled up
   */
  async checkLevelUp(): Promise<boolean> {
    const user = await this.getCurrentUser();
    if (!user) return false;

    const newLevel = this.calculateLevel(user.experience);
    return newLevel > user.level;
  }

  /**
   * Get level requirements for a specific level
   */
  async getLevelRequirements(level: number): Promise<LevelRequirement | null> {
    // Mock level requirements - in a real app this would come from a repository
    const baseExperience = 1000;
    const experienceRequired = Math.floor(baseExperience * Math.pow(1.5, level - 1));
    
    const unlocks = this.getLevelUnlocks(level);

    return {
      level,
      experienceRequired,
      unlocks
    };
  }

  /**
   * Add credits to user account
   */
  async addCredits(amount: number): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) return;

    await this.updateProfile({
      credits: user.credits + amount
    });
  }

  /**
   * Spend credits from user account
   */
  async spendCredits(amount: number): Promise<boolean> {
    const user = await this.getCurrentUser();
    if (!user || user.credits < amount) {
      return false;
    }

    await this.updateProfile({
      credits: user.credits - amount
    });

    return true;
  }

  /**
   * Add reputation points
   */
  async addReputation(amount: number): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) return;

    await this.updateProfile({
      reputation: user.reputation + amount
    });
  }

  /**
   * Check for new achievements
   */
  async checkAchievements(): Promise<Achievement[]> {
    const user = await this.getCurrentUser();
    if (!user) return [];

    const newAchievements: Achievement[] = [];

    // Check various achievement conditions
    if (user.stats.missionsCompleted >= 1 && !this.hasAchievement(user, 'ach-1')) {
      newAchievements.push(await this.unlockAchievementInternal('ach-1'));
    }

    if (user.stats.specialistsHired >= 1 && !this.hasAchievement(user, 'ach-2')) {
      newAchievements.push(await this.unlockAchievementInternal('ach-2'));
    }

    if (user.credits >= 1000000 && !this.hasAchievement(user, 'ach-5')) {
      newAchievements.push(await this.unlockAchievementInternal('ach-5'));
    }

    return newAchievements;
  }

  /**
   * Unlock a specific achievement
   */
  async unlockAchievement(achievementId: string): Promise<void> {
    await this.userRepository.unlockAchievement(this.currentUserId, achievementId);
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<UserPreferences> {
    return this.userRepository.getUserPreferences(this.currentUserId);
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<void> {
    await this.userRepository.updateUserPreferences(this.currentUserId, preferences);
  }

  /**
   * Update user statistics
   */
  async updateStats(statUpdates: Partial<UserProfile['stats']>): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) return;

    const updatedStats = { ...user.stats, ...statUpdates };
    await this.updateProfile({ stats: updatedStats });

    // Check for stat-based achievements
    await this.checkAchievements();
  }

  /**
   * Get player's rank (mock implementation)
   */
  async getPlayerRank(): Promise<number> {
    const user = await this.getCurrentUser();
    if (!user) return 0;

    // Mock ranking based on reputation and level
    return Math.floor(user.reputation / 100) + user.level;
  }

  /**
   * Calculate level based on experience
   */
  private calculateLevel(experience: number): number {
    const baseExperience = 1000;
    let level = 1;
    let requiredExp = baseExperience;

    while (experience >= requiredExp) {
      level++;
      requiredExp += Math.floor(baseExperience * Math.pow(1.5, level - 2));
    }

    return level;
  }

  /**
   * Calculate experience needed for next level
   */
  private calculateExperienceToNext(level: number, currentExp: number): number {
    const baseExperience = 1000;
    const nextLevelExp = Math.floor(baseExperience * Math.pow(1.5, level));
    return Math.max(0, nextLevelExp - currentExp);
  }

  /**
   * Get unlocks for a specific level
   */
  private getLevelUnlocks(level: number): string[] {
    const unlocks: Record<number, string[]> = {
      5: ['Gray Market Access'],
      10: ['Dark Market Access', 'Advanced Missions'],
      15: ['Elite Specialists', 'High-Risk Operations'],
      20: ['Master Tier Missions', 'Faction Leadership']
    };

    return unlocks[level] || [];
  }

  /**
   * Check if user has a specific achievement
   */
  private hasAchievement(user: UserProfile, achievementId: string): boolean {
    return user.achievements.some(ach => ach.id === achievementId);
  }

  /**
   * Internal method to unlock achievement
   */
  private async unlockAchievementInternal(achievementId: string): Promise<Achievement> {
    await this.unlockAchievement(achievementId);
    
    // Mock achievement data - in real app this would come from repository
    const mockAchievements: Record<string, Achievement> = {
      'ach-1': {
        id: 'ach-1',
        name: 'First Steps',
        description: 'Complete your first mission',
        icon: '🎯',
        unlockedAt: new Date().toISOString(),
        rarity: 'common'
      },
      'ach-2': {
        id: 'ach-2',
        name: 'Team Builder',
        description: 'Hire your first specialist',
        icon: '👥',
        unlockedAt: new Date().toISOString(),
        rarity: 'common'
      },
      'ach-5': {
        id: 'ach-5',
        name: 'Millionaire',
        description: 'Accumulate 1,000,000 credits',
        icon: '💰',
        unlockedAt: new Date().toISOString(),
        rarity: 'rare'
      }
    };

    return mockAchievements[achievementId];
  }

  /**
   * Check for level-up specific achievements
   */
  private async checkLevelUpAchievements(newLevel: number): Promise<void> {
    // Add level-specific achievement checks here
    if (newLevel === 10) {
      await this.unlockAchievement('level-10');
    }
    if (newLevel === 25) {
      await this.unlockAchievement('level-25');
    }
  }
}
