/* Modern UI Effects */

/* Ocean Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-strong {
  background: var(--bg-elevated);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--border-radius-lg);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.glass-ocean {
  background: var(--bg-hover);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  box-shadow:
    0 8px 32px rgba(0, 57, 82, 0.2),
    inset 0 1px 0 rgba(0, 179, 164, 0.1);
}

.glass-accent {
  background: var(--bg-accent);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 179, 164, 0.2);
  border-radius: var(--border-radius-lg);
  box-shadow:
    0 6px 24px rgba(0, 179, 164, 0.1),
    inset 0 1px 0 rgba(0, 179, 164, 0.2);
}

/* Subtle gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, 
    var(--bg-secondary) 0%, 
    var(--bg-tertiary) 50%, 
    var(--bg-secondary) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, 
    var(--primary-600) 0%, 
    var(--secondary-600) 100%);
}

/* Modern card effect */
.modern-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-subtle);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    var(--primary-500), 
    transparent);
  opacity: 0.5;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-color);
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Pulse effect */
@keyframes modernPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.pulse {
  animation: modernPulse 2s ease-in-out infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth reveal animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-out;
}

/* Modern focus ring */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500);
  border-radius: inherit;
}

/* Subtle border glow */
.border-glow {
  position: relative;
}

.border-glow::before {
  content: '';
  position: absolute;
  inset: -1px;
  padding: 1px;
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0.6;
}

/* Interactive hover effects */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Loading skeleton */
@keyframes skeleton {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-tertiary) 0%,
    var(--bg-secondary) 50%,
    var(--bg-tertiary) 100%
  );
  background-size: 200px 100%;
  animation: skeleton 1.5s ease-in-out infinite;
  border-radius: var(--border-radius-md);
}

/* Smooth transitions */
.smooth-transition {
  transition: all var(--transition-normal);
}

/* Modern scrollbar */
.modern-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius-full);
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--border-active);
}

/* Backdrop blur utility */
.backdrop-blur {
  backdrop-filter: blur(10px);
}

.backdrop-blur-strong {
  backdrop-filter: blur(20px);
}



/* Modern button press effect */
.press-effect {
  transition: transform 0.1s ease;
}

.press-effect:active {
  transform: scale(0.98);
}
