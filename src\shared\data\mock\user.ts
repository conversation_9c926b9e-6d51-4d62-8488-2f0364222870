/**
 * Mock user/player data for development and testing
 */

export interface UserProfile {
  id: string;
  username: string;
  level: number;
  experience: number;
  experienceToNext: number;
  credits: number;
  reputation: number;
  securityLevel: number;
  joinDate: string;
  lastActive: string;
  stats: {
    missionsCompleted: number;
    missionsSuccessful: number;
    totalEarnings: number;
    specialistsHired: number;
    daysActive: number;
  };
  achievements: Achievement[];
  preferences: UserPreferences;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'auto';
  notifications: {
    missions: boolean;
    market: boolean;
    team: boolean;
    security: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
    shareStatistics: boolean;
  };
  interface: {
    compactMode: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
  };
}

export const mockUserProfile: UserProfile = {
  id: 'user-1',
  username: 'CyberOperator',
  level: 15,
  experience: 12750,
  experienceToNext: 2250,
  credits: 125000,
  reputation: 850,
  securityLevel: 7,
  joinDate: '2024-01-15T10:30:00Z',
  lastActive: '2024-12-20T14:45:00Z',
  stats: {
    missionsCompleted: 42,
    missionsSuccessful: 38,
    totalEarnings: 485000,
    specialistsHired: 12,
    daysActive: 89
  },
  achievements: [
    {
      id: 'ach-1',
      name: 'First Steps',
      description: 'Complete your first mission',
      icon: '🎯',
      unlockedAt: '2024-01-16T09:15:00Z',
      rarity: 'common'
    },
    {
      id: 'ach-2',
      name: 'Team Builder',
      description: 'Hire your first specialist',
      icon: '👥',
      unlockedAt: '2024-01-18T16:22:00Z',
      rarity: 'common'
    },
    {
      id: 'ach-3',
      name: 'Ghost in the Machine',
      description: 'Complete a mission without being detected',
      icon: '👻',
      unlockedAt: '2024-02-05T11:30:00Z',
      rarity: 'rare'
    },
    {
      id: 'ach-4',
      name: 'Master Infiltrator',
      description: 'Successfully infiltrate 10 high-security targets',
      icon: '🔓',
      unlockedAt: '2024-03-12T20:45:00Z',
      rarity: 'epic'
    },
    {
      id: 'ach-5',
      name: 'Millionaire',
      description: 'Accumulate 1,000,000 credits',
      icon: '💰',
      unlockedAt: '2024-11-08T13:20:00Z',
      rarity: 'rare'
    }
  ],
  preferences: {
    theme: 'dark',
    notifications: {
      missions: true,
      market: true,
      team: true,
      security: true
    },
    privacy: {
      showOnlineStatus: true,
      allowDirectMessages: false,
      shareStatistics: true
    },
    interface: {
      compactMode: false,
      showTooltips: true,
      animationsEnabled: true
    }
  }
};

export const mockUserStats = mockUserProfile.stats;
export const mockUserAchievements = mockUserProfile.achievements;
