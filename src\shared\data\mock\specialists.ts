/**
 * Mock specialist data for development and testing
 */

import { Specialist, SpecializationType, SpecialistStatus } from '@/core/specialist/types';

export const mockSpecialists: Specialist[] = [
  {
    id: 'specialist-1',
    name: '<PERSON>',
    specialization: 'network_infiltrator',
    level: 3,
    experience: 1250,
    skills: {
      hacking: 85,
      network_infiltration: 90,
      cryptography: 70,
      social_engineering: 45,
      malware_development: 60,
      exploitation: 80,
      persuasion: 40,
      deception: 35,
      intel_analysis: 55,
      osint: 65,
      threat_hunting: 50,
      physical_security: 30,
      surveillance: 40,
      counter_surveillance: 35
    },
    traits: [
      {
        type: 'meticulous',
        name: 'Meticulous',
        description: 'Pays attention to detail and rarely makes mistakes',
        effect: 'Reduces chance of detection by 15%',
        isPositive: true
      },
      {
        type: 'innovative',
        name: 'Innovative',
        description: 'Thinks outside the box and finds creative solutions',
        effect: 'Occasionally provides alternative mission approaches',
        isPositive: true
      }
    ],
    status: 'available',
    cost: 5000,
    availability: true,
    stats: {
      technical: 85,
      stealth: 70,
      social: 60,
      physical: 55
    }
  },
  {
    id: 'specialist-2',
    name: '<PERSON>',
    specialization: 'social_engineer',
    level: 4,
    experience: 2100,
    skills: {
      hacking: 45,
      network_infiltration: 40,
      cryptography: 35,
      social_engineering: 95,
      malware_development: 25,
      exploitation: 50,
      persuasion: 90,
      deception: 85,
      intel_analysis: 70,
      osint: 80,
      threat_hunting: 60,
      physical_security: 65,
      surveillance: 75,
      counter_surveillance: 70
    },
    traits: [
      {
        type: 'connected',
        name: 'Well Connected',
        description: 'Has contacts in various organizations',
        effect: 'Provides additional intelligence sources',
        isPositive: true
      },
      {
        type: 'calm',
        name: 'Calm Under Pressure',
        description: 'Maintains composure in stressful situations',
        effect: 'Better performance during high-risk missions',
        isPositive: true
      }
    ],
    status: 'available',
    cost: 7500,
    availability: true,
    stats: {
      technical: 45,
      stealth: 80,
      social: 95,
      physical: 70
    }
  },
  {
    id: 'specialist-3',
    name: 'Viktor Petrov',
    specialization: 'malware_developer',
    level: 5,
    experience: 3500,
    skills: {
      hacking: 90,
      network_infiltration: 75,
      cryptography: 85,
      social_engineering: 30,
      malware_development: 95,
      exploitation: 90,
      persuasion: 25,
      deception: 40,
      intel_analysis: 80,
      osint: 60,
      threat_hunting: 85,
      physical_security: 20,
      surveillance: 35,
      counter_surveillance: 45
    },
    traits: [
      {
        type: 'efficient',
        name: 'Highly Efficient',
        description: 'Completes tasks faster than average',
        effect: 'Reduces mission time by 20%',
        isPositive: true
      },
      {
        type: 'impatient',
        name: 'Impatient',
        description: 'Sometimes rushes through tasks',
        effect: 'Slightly increased risk of detection',
        isPositive: false
      }
    ],
    status: 'on_mission',
    cost: 10000,
    availability: false,
    stats: {
      technical: 95,
      stealth: 60,
      social: 30,
      physical: 40
    }
  },
  {
    id: 'specialist-4',
    name: 'Sarah Kim',
    specialization: 'osint_analyst',
    level: 2,
    experience: 800,
    skills: {
      hacking: 55,
      network_infiltration: 45,
      cryptography: 60,
      social_engineering: 70,
      malware_development: 30,
      exploitation: 40,
      persuasion: 65,
      deception: 55,
      intel_analysis: 90,
      osint: 95,
      threat_hunting: 85,
      physical_security: 40,
      surveillance: 80,
      counter_surveillance: 75
    },
    traits: [
      {
        type: 'loyal',
        name: 'Loyal',
        description: 'Extremely loyal and trustworthy',
        effect: 'Resistant to compromise attempts',
        isPositive: true
      }
    ],
    status: 'training',
    cost: 4000,
    availability: false,
    stats: {
      technical: 60,
      stealth: 75,
      social: 70,
      physical: 45
    }
  },
  {
    id: 'specialist-5',
    name: 'Marcus Thompson',
    specialization: 'physical_security_specialist',
    level: 3,
    experience: 1500,
    skills: {
      hacking: 40,
      network_infiltration: 35,
      cryptography: 30,
      social_engineering: 60,
      malware_development: 20,
      exploitation: 45,
      persuasion: 70,
      deception: 65,
      intel_analysis: 55,
      osint: 50,
      threat_hunting: 45,
      physical_security: 90,
      surveillance: 85,
      counter_surveillance: 80
    },
    traits: [
      {
        type: 'calm',
        name: 'Calm Under Pressure',
        description: 'Maintains composure in dangerous situations',
        effect: 'Better performance during physical infiltration',
        isPositive: true
      },
      {
        type: 'careless',
        name: 'Occasionally Careless',
        description: 'Sometimes overlooks important details',
        effect: 'Small chance of mission complications',
        isPositive: false
      }
    ],
    status: 'available',
    cost: 6000,
    availability: true,
    stats: {
      technical: 40,
      stealth: 85,
      social: 65,
      physical: 90
    }
  }
];

export const mockAvailableSpecialists = mockSpecialists.filter(s => s.status === 'available');
export const mockHiredSpecialists = mockSpecialists.filter(s => s.status !== 'available');
