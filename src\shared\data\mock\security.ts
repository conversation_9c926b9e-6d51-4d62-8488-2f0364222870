/**
 * Mock security/OPSEC data for development and testing
 */

export interface SecurityThreat {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'surveillance' | 'forensic' | 'network' | 'physical' | 'social';
  detectedAt: string;
  status: 'active' | 'mitigated' | 'investigating';
  source?: string;
  recommendations: string[];
}

export interface SecurityMetrics {
  overallLevel: number;
  networkSecurity: number;
  physicalSecurity: number;
  operationalSecurity: number;
  digitalFootprint: number;
  traceLevel: number;
  lastAssessment: string;
}

export interface Countermeasure {
  id: string;
  name: string;
  description: string;
  type: 'preventive' | 'detective' | 'corrective';
  cost: number;
  effectiveness: number;
  duration: number; // in hours
  requirements: string[];
  active: boolean;
}

export const mockSecurityThreats: SecurityThreat[] = [
  {
    id: 'threat-1',
    name: 'Unusual Network Traffic',
    description: 'Detected anomalous outbound connections to unknown IP addresses',
    severity: 'medium',
    type: 'network',
    detectedAt: '2024-12-20T10:15:00Z',
    status: 'investigating',
    source: 'Network Monitoring System',
    recommendations: [
      'Review firewall logs',
      'Check for malware infections',
      'Verify VPN connections'
    ]
  },
  {
    id: 'threat-2',
    name: 'Surveillance Detection',
    description: 'Possible physical surveillance detected near safe house location',
    severity: 'high',
    type: 'physical',
    detectedAt: '2024-12-19T16:30:00Z',
    status: 'active',
    source: 'Field Agent Report',
    recommendations: [
      'Activate counter-surveillance protocols',
      'Consider location change',
      'Increase security measures'
    ]
  },
  {
    id: 'threat-3',
    name: 'Digital Footprint Exposure',
    description: 'Increased online activity correlation detected across multiple platforms',
    severity: 'medium',
    type: 'surveillance',
    detectedAt: '2024-12-18T09:45:00Z',
    status: 'mitigated',
    source: 'OSINT Analysis',
    recommendations: [
      'Rotate online identities',
      'Implement better compartmentalization',
      'Review social media activity'
    ]
  },
  {
    id: 'threat-4',
    name: 'Forensic Analysis Risk',
    description: 'Target system may have advanced logging and forensic capabilities',
    severity: 'high',
    type: 'forensic',
    detectedAt: '2024-12-17T14:20:00Z',
    status: 'active',
    source: 'Mission Intelligence',
    recommendations: [
      'Deploy anti-forensic tools',
      'Use live-boot systems',
      'Implement secure deletion protocols'
    ]
  }
];

export const mockSecurityMetrics: SecurityMetrics = {
  overallLevel: 7,
  networkSecurity: 8,
  physicalSecurity: 6,
  operationalSecurity: 7,
  digitalFootprint: 5,
  traceLevel: 3,
  lastAssessment: '2024-12-20T08:00:00Z'
};

export const mockCountermeasures: Countermeasure[] = [
  {
    id: 'counter-1',
    name: 'VPN Rotation',
    description: 'Automatically rotate VPN endpoints every 30 minutes',
    type: 'preventive',
    cost: 500,
    effectiveness: 85,
    duration: 24,
    requirements: ['VPN Service', 'Automation Scripts'],
    active: true
  },
  {
    id: 'counter-2',
    name: 'Traffic Obfuscation',
    description: 'Disguise network traffic patterns using decoy connections',
    type: 'preventive',
    cost: 750,
    effectiveness: 70,
    duration: 12,
    requirements: ['Proxy Network', 'Traffic Generator'],
    active: false
  },
  {
    id: 'counter-3',
    name: 'Digital Dead Drop',
    description: 'Secure communication channel using steganography',
    type: 'preventive',
    cost: 1200,
    effectiveness: 90,
    duration: 48,
    requirements: ['Steganography Tools', 'Secure Channels'],
    active: true
  },
  {
    id: 'counter-4',
    name: 'Counter-Surveillance',
    description: 'Active monitoring for physical surveillance attempts',
    type: 'detective',
    cost: 2000,
    effectiveness: 80,
    duration: 8,
    requirements: ['Surveillance Equipment', 'Trained Personnel'],
    active: false
  },
  {
    id: 'counter-5',
    name: 'Emergency Protocols',
    description: 'Rapid response procedures for security breaches',
    type: 'corrective',
    cost: 1500,
    effectiveness: 95,
    duration: 1,
    requirements: ['Emergency Kit', 'Escape Routes'],
    active: true
  }
];

export const mockActiveThreats = mockSecurityThreats.filter(t => t.status === 'active');
export const mockActiveCountermeasures = mockCountermeasures.filter(c => c.active);
