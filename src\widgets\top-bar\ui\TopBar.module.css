.topBar {
  display: flex;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(20px);
  background: var(--bg-elevated);
  border-radius: var(--border-radius-xl);

  padding: var(--space-sm);
  box-shadow: var(--shadow-lg);
  z-index: 10;
  position: relative;
}

.topBarContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  backdrop-filter: blur(10px);
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.logo {
  font-size: 1.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-400);
  transition: color var(--transition-fast);
}

.logo:hover {
  color: var(--primary-300);
}

.name {
  font-family: var(--font-display);
  font-weight: 600;
  letter-spacing: -0.025em;
  color: var(--text-on-card);
  font-size: var(--font-size-md);
}

.widgetsArea {
  display: flex;
  align-items: center;
  gap: var(--space-xl);
  padding: 0 var(--space-lg);
}

.widget {
  height: 100%;
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: color var(--transition-fast);
  font-weight: 500;
}

.widget:hover {
  color: var(--text-primary);
}

/* Specific widgets styling */
.timeWidget,
.dateWidget,
.securityWidget,
.creditsWidget {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-lg);
  background: transparent;
  color: var(--text-on-card);
  font-size: var(--font-size-sm);
}

.actionArea {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--border-radius-lg);
  background: transparent;
  border: none;
  color: var(--text-on-card);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
}

.actionButton:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.actionButton.active {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.15);
}

.iconWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 18px;
}

.notificationDot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-error);
  box-shadow: 0 0 0 2px var(--bg-elevated);
  animation: pulse 2s infinite;
}

/* Configuration Panel */
.configPanel {
  position: absolute;
  top: 100%;
  right: 20px;
  width: 250px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-md);
  z-index: var(--z-dropdown);
  animation: fadeIn 0.2s ease-out;
}

.configPanel h3 {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-md);
  color: var(--text-accent);
  text-align: center;
}

.widgetList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widgetItem {
  margin-bottom: var(--space-sm);
  padding: var(--space-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.widgetToggle {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  cursor: pointer;
}

.widgetToggle input[type="checkbox"] {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-tertiary);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.widgetToggle input[type="checkbox"]:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.widgetToggle input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 4px;
  height: 8px;
  border: solid var(--text-on-accent);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.widgetName {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
