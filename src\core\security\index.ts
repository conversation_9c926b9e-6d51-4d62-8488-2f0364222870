// Security module exports
import { SecurityService } from './SecurityService';
import { ISecurityService } from './interfaces';
import { MockSecurityRepository } from '../../features/security/repository';

// Create repository
const securityRepository = new MockSecurityRepository();

// Create and export security service
export const securityService: ISecurityService = new SecurityService(securityRepository);

// Export types and interfaces
export * from './types';
export * from './interfaces';
export { SecurityService } from './SecurityService';
