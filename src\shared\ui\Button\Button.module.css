.button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-main);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  letter-spacing: -0.025em;
  border: 1px solid transparent;
  gap: var(--space-xs);
  backdrop-filter: blur(10px);
}

/* Ocean Theme Button Variants */
.variant-primary {
  background: var(--bg-accent);
  color: var(--text-on-card);
  font-weight: 600;
  box-shadow: var(--shadow-md);
  border: none;
  backdrop-filter: blur(10px);
}

.variant-primary:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.variant-primary:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

.variant-secondary {
  background: var(--bg-hover);
  border: none;
  color: var(--text-on-card);
  backdrop-filter: blur(10px);
}

.variant-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.variant-secondary:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

.variant-danger {
  background: linear-gradient(135deg, var(--color-error), #dc2626);
  color: var(--text-on-accent);
  border: 1px solid var(--color-error);
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.variant-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
  border-color: #dc2626;
}

.variant-danger:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-sm);
}

.variant-ghost {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid transparent;
}

.variant-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.variant-ghost:active {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(0px);
}



/* Button Sizes */
.size-sm {
  height: 30px;
  padding: 0 var(--space-sm);
  font-size: var(--font-size-xs);
}

.size-md {
  height: 36px;
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
}

.size-lg {
  height: 44px;
  padding: 0 var(--space-lg);
  font-size: var(--font-size-md);
}

/* Full Width */
.fullWidth {
  width: 100%;
}

/* Loading State */
.loading {
  cursor: wait;
  opacity: 0.8;
}

.loading .content {
  visibility: hidden;
}

.spinner {
  position: absolute;
  width: 18px;
  height: 18px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
}

/* Disabled State */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none;
}

.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

/* Content container */
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
}

/* Icons */
.leftIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  line-height: 0;
}

.rightIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  line-height: 0;
}

/* Shine Effect */
.button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
  pointer-events: none;
}

.button:hover::after {
  left: 100%;
}

/* Focus state */
.button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.3);
}

.variant-primary:focus {
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.5), 0 0 10px rgba(0, 240, 255, 0.5);
}

.variant-danger:focus {
  box-shadow: 0 0 0 2px rgba(255, 0, 103, 0.5), 0 0 10px rgba(255, 0, 103, 0.5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



