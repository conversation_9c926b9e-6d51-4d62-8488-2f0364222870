.dashboardPage {
  display: flex;
  flex-direction: column;
  height: 100%;

}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-hover);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-lg);
  padding: var(--space-sm) var(--space-md);
  margin: var(--space-sm);
  
}

.dashboardTitle {
  margin: 0;
  font-size: var(--font-size-md);
  text-transform: uppercase;
  font-weight: 600;
  color: var(--text-on-card);
}

.welcomeMessage {
  font-size: var(--font-size-sm);
  color: var(--text-on-card);
}

.dashboardContent {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
overflow-x: hidden;

}

.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 24px;
}

.sectionTitle {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #E0E0E0;
}

/* Quick Actions section removed */

/* Active Missions */
.activeMissions {
  grid-column: 1;
  grid-row: 1;
}

.missionsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.missionCard {
  padding: var(--space-md);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
}

.missionCard:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.missionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.missionTitle {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-on-card);
}

.missionDifficulty {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.novice {
  background-color: rgba(52, 199, 89, 0.2);
  color: #34C759;
}

.professional {
  background-color: rgba(0, 122, 255, 0.2);
  color: #007AFF;
}

.expert {
  background-color: rgba(255, 149, 0, 0.2);
  color: #FF9500;
}

.elite, .legendary {
  background-color: rgba(255, 45, 85, 0.2);
  color: #FF2D55;
}

.missionDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.missionFaction {
  font-size: 14px;
  color: #B0B0B0;
}

.progressContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #B0B0B0;
}

.progressBar {
  height: 6px;
  background-color: #333333;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background-color: #00F0FF;
  border-radius: 3px;
}

/* Recent Events */
.recentEvents {
  grid-column: 2;
  grid-row: 1;
}

.eventsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.eventItem {
  display: flex;
  align-items: center;
  padding: var(--space-sm);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border-left: 3px solid transparent;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
}

.eventItem:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.eventItem.info {
  border-left-color: #007AFF;
}

.eventItem.success {
  border-left-color: #34C759;
}

.eventItem.warning {
  border-left-color: #FF9500;
}

.eventItem.error {
  border-left-color: #FF2D55;
}

.eventTime {
  font-size: 12px;
  color: #B0B0B0;
  margin-right: 12px;
  min-width: 60px;
}

.eventTitle {
  font-size: 14px;
}

/* Resource Summary */
.resourceSummary {
  grid-column: 1;
  grid-row: 2;
}

.resourceGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.resourceCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-md);
  background: var(--bg-hover);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
}

.resourceCard:hover {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.resourceIcon {
  font-size: 24px;
  margin-bottom: 8px;
}

.resourceName {
  font-size: var(--font-size-sm);
  color: var(--text-on-card);
  margin-bottom: 4px;
}

.resourceValue {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-on-card);
}

/* Faction Standings */
.factionStandings {
  grid-column: 2;
  grid-row: 2;
}

.standingsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.standingItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.factionName {
  flex: 0 0 150px;
  font-size: 14px;
}

.standingBar {
  flex: 1;
  height: 8px;
  background-color: #333333;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.standingFill {
  height: 100%;
  border-radius: 4px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.standingFill.positive {
  background-color: #34C759;
  transform: translateX(0);
  left: 0;
}

.standingFill.negative {
  background-color: #FF3B30;
  transform: translateX(-100%);
  left: 50%;
}

.standingValue {
  flex: 0 0 40px;
  text-align: right;
  font-weight: 500;
}

.emptyState {
  padding: 24px;
  text-align: center;
  background-color: #2A2A2A;
  border-radius: 8px;
  color: #707070;
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
  
  .activeMissions,
  .recentEvents,
  .resourceSummary,
  .factionStandings {
    grid-column: 1;
  }
  
  .activeMissions {
    grid-row: 1;
  }
  
  .recentEvents {
    grid-row: 2;
  }
  
  .resourceSummary {
    grid-row: 3;
  }
  
  .factionStandings {
    grid-row: 4;
  }
}
