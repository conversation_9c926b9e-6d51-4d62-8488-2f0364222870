/**
 * Core domain types for User/Player functionality
 */

export interface UserProfile {
  id: string;
  username: string;
  level: number;
  experience: number;
  experienceToNext: number;
  credits: number;
  reputation: number;
  securityLevel: number;
  joinDate: string;
  lastActive: string;
  stats: UserStats;
  achievements: Achievement[];
  preferences: UserPreferences;
}

export interface UserStats {
  missionsCompleted: number;
  missionsSuccessful: number;
  totalEarnings: number;
  specialistsHired: number;
  daysActive: number;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'auto';
  notifications: {
    missions: boolean;
    market: boolean;
    team: boolean;
    security: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
    shareStatistics: boolean;
  };
  interface: {
    compactMode: boolean;
    showTooltips: boolean;
    animationsEnabled: boolean;
  };
}

export interface UserResource {
  type: 'credits' | 'experience' | 'reputation';
  amount: number;
  lastUpdated: string;
}

export interface LevelRequirement {
  level: number;
  experienceRequired: number;
  unlocks: string[];
}
