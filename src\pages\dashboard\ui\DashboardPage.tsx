import React from 'react';
import styles from './DashboardPage.module.css';
import { mockActiveMissions } from '@/shared/data/mock/missions';
import {
  mockRecentEvents,
  mockDashboardStats,
  mockSystemStatus
} from '@/shared/data/mock/dashboard';

interface DashboardPageProps {
  tabId: string;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ tabId }) => {
  // Use mock data from shared data
  const activeMissions = mockActiveMissions;
  const recentEvents = mockRecentEvents;
  const dashboardStats = mockDashboardStats;

  return (
    <div className={styles.dashboardPage}>
      <div className={styles.dashboardHeader}>
        <h1 className={styles.dashboardTitle}>Operations Dashboard</h1>
        <span className={styles.welcomeMessage}>Welcome back, Operator</span>
      </div>

      <div className={styles.dashboardContent}>
        {/* Resource summary section */}
        <section className={styles.resourceSummary}>
          <h2 className={styles.sectionTitle}>Resources</h2>
          <div className={styles.resourceGrid}>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.credits.toLocaleString()}</span>
              <span className={styles.resourceName}>Credits</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.activeMissions}</span>
              <span className={styles.resourceName}>Active Missions</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.availableSpecialists}</span>
              <span className={styles.resourceName}>Available Team</span>
            </div>
            <div className={styles.resourceCard}>
              <span className={styles.resourceValue}>{dashboardStats.reputation}</span>
              <span className={styles.resourceName}>Reputation</span>
            </div>
          </div>
        </section>

        {/* Active missions section */}
        <section className={styles.activeMissions}>
          <h2 className={styles.sectionTitle}>Active Missions</h2>
          <div className={styles.missionsList}>
            {activeMissions.map(mission => (
              <div key={mission.id} className={styles.missionCard}>
                <div className={styles.missionHeader}>
                  <h3 className={styles.missionTitle}>{mission.title}</h3>
                  <span className={`${styles.difficultyBadge} ${styles[mission.difficulty]}`}>
                    {mission.difficulty}
                  </span>
                </div>
                <div className={styles.missionDetails}>
                  <span className={styles.missionFaction}>{mission.faction}</span>
                  <div className={styles.progressBar}>
                    <div
                      className={styles.progressFill}
                      style={{ width: `${mission.progress}%` }}
                    />
                  </div>
                  <span className={styles.progressText}>{mission.progress}%</span>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Recent events section */}
        <section className={styles.recentEvents}>
          <h2 className={styles.sectionTitle}>Recent Events</h2>
          <div className={styles.eventsList}>
            {recentEvents.map(event => (
              <div key={event.id} className={`${styles.eventItem} ${styles[event.type]}`}>
                <div className={styles.eventContent}>
                  <span className={styles.eventTitle}>{event.title}</span>
                  <span className={styles.eventTime}>{event.time}</span>
                </div>
                {event.description && (
                  <span className={styles.eventDescription}>{event.description}</span>
                )}
              </div>
            ))}
          </div>
        </section>
        
        <div className={styles.dashboardGrid}>
          {/* Active missions section */}
          <section className={styles.activeMissions}>
            <h2 className={styles.sectionTitle}>Active Missions</h2>
            {activeMissions.length === 0 ? (
              <p className={styles.emptyState}>No active missions</p>
            ) : (
              <div className={styles.missionsList}>
                {activeMissions.map(mission => (
                  <div key={mission.id} className={styles.missionCard}>
                    <div className={styles.missionHeader}>
                      <h3 className={styles.missionTitle}>{mission.title}</h3>
                      <span className={`${styles.missionDifficulty} ${styles[mission.difficulty]}`}>
                        {mission.difficulty}
                      </span>
                    </div>
                    <div className={styles.missionDetails}>
                      <span className={styles.missionFaction}>{mission.faction}</span>
                      <div className={styles.progressContainer}>
                        <div className={styles.progressLabel}>Progress: {mission.progress}%</div>
                        <div className={styles.progressBar}>
                          <div 
                            className={styles.progressFill} 
                            style={{ width: `${mission.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </section>
          
          {/* Recent events section */}
          <section className={styles.recentEvents}>
            <h2 className={styles.sectionTitle}>Recent Events</h2>
            {recentEvents.length === 0 ? (
              <p className={styles.emptyState}>No recent events</p>
            ) : (
              <div className={styles.eventsList}>
                {recentEvents.map(event => (
                  <div key={event.id} className={`${styles.eventItem} ${styles[event.type]}`}>
                    <span className={styles.eventTime}>{event.time}</span>
                    <span className={styles.eventTitle}>{event.title}</span>
                  </div>
                ))}
              </div>
            )}
          </section>
          
          {/* Resource summary section */}
          <section className={styles.resourceSummary}>
            <h2 className={styles.sectionTitle}>Resources</h2>
            <div className={styles.resourceGrid}>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>{dashboardStats.credits.toLocaleString()}</span>
                <span className={styles.resourceName}>Credits</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>{dashboardStats.activeMissions}</span>
                <span className={styles.resourceName}>Active Missions</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>{dashboardStats.availableSpecialists}</span>
                <span className={styles.resourceName}>Available Team</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>{dashboardStats.reputation}</span>
                <span className={styles.resourceName}>Reputation</span>
              </div>
            </div>
          </section>
          
          {/* Faction standings section */}
          <section className={styles.factionStandings}>
            <h2 className={styles.sectionTitle}>Faction Standings</h2>
            <div className={styles.standingsList}>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Crime Syndicate</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '65%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+65</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>State-Sponsored</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '30%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+30</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Hacktivists</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.negative}`} 
                    style={{ width: '20%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>-20</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Security Operations</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '10%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+10</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
