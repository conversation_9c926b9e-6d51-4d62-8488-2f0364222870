.bottomBar {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  position: relative;
}

.appLauncher {
  display: flex;
  background-color: var(--bg-elevated);
  gap: var(--space-sm);
  align-items: center;
  justify-content: center;
  padding: var(--space-sm);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.appButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-on-card);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-accent);
  text-transform: uppercase;
  font-weight: 500;
  font-size: var(--font-size-xs);
  backdrop-filter: blur(5px);
  border: none;
}



.appButton:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.appButton.active {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: none;
}



.iconWrapper {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.iconWrapper.active {
  color: var(--primary-400);
}

.appName {
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
}

/* Removed systemInfo styles as they're no longer needed */
