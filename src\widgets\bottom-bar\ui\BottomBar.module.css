.bottomBar {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  position: relative;
}

.appLauncher {
  display: flex;
  background-color: var(--bg-elevated);
  gap: var(--space-sm);
  align-items: center;
  justify-content: center;
  padding: var(--space-sm);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.appButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-on-card);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-accent);
  text-transform: uppercase;
  font-weight: 500;
  font-size: var(--font-size-xs);
  backdrop-filter: blur(5px);
  border: none;
}



.appButton:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: none;
}

.appButton.active {
  color: var(--text-primary);
  background: rgba(0, 179, 164, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 0 20px rgba(0, 179, 164, 0.3);
}

.appButton.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
}

.appButton.disabled:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-on-card);
}

.iconWrapper {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
}

.iconWrapper.active {
  color: var(--primary-400);
}

.appName {
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
}

.activeIndicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--primary-400);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--primary-400);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.tabCounter {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: var(--space-md);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(0, 179, 164, 0.1);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(0, 179, 164, 0.3);
}

.counterText {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--primary-400);
  font-family: monospace;
}
