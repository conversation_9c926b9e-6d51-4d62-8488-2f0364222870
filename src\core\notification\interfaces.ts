/**
 * Core domain interfaces for Notification functionality
 */

import {
  Notification,
  NotificationSettings,
  NotificationTemplate,
  NotificationStats,
  NotificationType,
  NotificationPriority,
  NotificationCategory
} from './types';

export interface NotificationRepository {
  // Notification CRUD
  getNotifications(limit?: number): Promise<Notification[]>;
  getNotificationById(id: string): Promise<Notification | null>;
  createNotification(notification: Omit<Notification, 'id' | 'timestamp'>): Promise<Notification>;
  updateNotification(id: string, updates: Partial<Notification>): Promise<void>;
  deleteNotification(id: string): Promise<void>;
  
  // Bulk operations
  markAllAsRead(): Promise<void>;
  dismissAll(): Promise<void>;
  deleteExpired(): Promise<void>;
  
  // Filtering
  getUnreadNotifications(): Promise<Notification[]>;
  getNotificationsByCategory(category: NotificationCategory): Promise<Notification[]>;
  getNotificationsByType(type: NotificationType): Promise<Notification[]>;
  
  // Settings
  getSettings(): Promise<NotificationSettings>;
  updateSettings(settings: Partial<NotificationSettings>): Promise<void>;
  
  // Templates
  getTemplates(): Promise<NotificationTemplate[]>;
  getTemplateById(id: string): Promise<NotificationTemplate | null>;
}

export interface INotificationService {
  // Core notification operations
  createNotification(
    title: string,
    message: string,
    type: NotificationType,
    options?: {
      priority?: NotificationPriority;
      category?: NotificationCategory;
      actionUrl?: string;
      actionText?: string;
      expiresIn?: number; // seconds
      metadata?: Record<string, any>;
    }
  ): Promise<Notification>;
  
  // Template-based notifications
  createFromTemplate(
    templateId: string,
    variables: Record<string, string>
  ): Promise<Notification>;
  
  // Notification management
  getNotifications(limit?: number): Promise<Notification[]>;
  getUnreadNotifications(): Promise<Notification[]>;
  markAsRead(notificationId: string): Promise<void>;
  markAllAsRead(): Promise<void>;
  dismissNotification(notificationId: string): Promise<void>;
  deleteNotification(notificationId: string): Promise<void>;
  
  // Filtering and search
  getNotificationsByCategory(category: NotificationCategory): Promise<Notification[]>;
  getNotificationsByPriority(priority: NotificationPriority): Promise<Notification[]>;
  searchNotifications(query: string): Promise<Notification[]>;
  
  // Statistics
  getNotificationStats(): Promise<NotificationStats>;
  
  // Settings
  getSettings(): Promise<NotificationSettings>;
  updateSettings(settings: Partial<NotificationSettings>): Promise<void>;
  
  // System notifications
  notifyMissionComplete(missionId: string, success: boolean): Promise<void>;
  notifySpecialistHired(specialistName: string): Promise<void>;
  notifySecurityThreat(threatName: string, severity: string): Promise<void>;
  notifyMarketOpportunity(itemName: string): Promise<void>;
  notifyAchievementUnlocked(achievementName: string): Promise<void>;
  
  // Cleanup
  cleanupExpiredNotifications(): Promise<void>;
  
  // Real-time features
  subscribeToNotifications(callback: (notification: Notification) => void): () => void;
}
