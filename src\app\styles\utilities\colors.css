/* Color utility classes */

/* Text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--text-accent); }
.text-warning { color: var(--text-warning); }
.text-danger { color: var(--text-danger); }
.text-success { color: var(--text-success); }
.text-info { color: var(--text-info); }

/* Modern accent text colors */
.text-primary-400 { color: var(--primary-400); }
.text-primary-500 { color: var(--primary-500); }
.text-secondary-400 { color: var(--secondary-400); }
.text-secondary-500 { color: var(--secondary-500); }

/* Background colors */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-accent { background-color: var(--bg-accent); }
.bg-hover { background-color: var(--bg-hover); }

/* Special backgrounds */
.bg-card { background-color: var(--bg-card); }
.bg-modal { background-color: var(--bg-modal); }
.bg-overlay { background-color: var(--bg-overlay); }
.bg-tooltip { background-color: var(--bg-tooltip); }

/* Modern gradient backgrounds */
.bg-gradient-primary { background: linear-gradient(135deg, var(--primary-600), var(--primary-500)); }
.bg-gradient-secondary { background: linear-gradient(135deg, var(--secondary-600), var(--secondary-500)); }
.bg-gradient-accent { background: linear-gradient(135deg, var(--primary-500), var(--secondary-500)); }



/* Borders */
.border { border: var(--border-width) var(--border-style) var(--border-color); }
.border-t { border-top: var(--border-width) var(--border-style) var(--border-color); }
.border-r { border-right: var(--border-width) var(--border-style) var(--border-color); }
.border-b { border-bottom: var(--border-width) var(--border-style) var(--border-color); }
.border-l { border-left: var(--border-width) var(--border-style) var(--border-color); }

.border-active { border-color: var(--border-active); }
.border-cyan { border-color: var(--neon-cyan); }
.border-pink { border-color: var(--neon-pink); }
.border-purple { border-color: var(--neon-purple); }
.border-yellow { border-color: var(--neon-yellow); }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-neon { box-shadow: var(--shadow-neon); }
.shadow-pink { box-shadow: var(--shadow-pink); }
.shadow-purple { box-shadow: var(--shadow-purple); }
.shadow-yellow { box-shadow: var(--shadow-yellow); }
.shadow-green { box-shadow: var(--shadow-green); }

/* Border radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: 9999px; }
