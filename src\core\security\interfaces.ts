/**
 * Core domain interfaces for Security/OPSEC functionality
 */

import {
  SecurityThreat,
  SecurityMetrics,
  Countermeasure,
  SecurityAlert,
  SecurityAssessment,
  SecurityEvent,
  ThreatSeverity,
  ThreatType
} from './types';

export interface SecurityRepository {
  // Threat management
  getThreats(): Promise<SecurityThreat[]>;
  getThreatById(id: string): Promise<SecurityThreat | null>;
  createThreat(threat: Omit<SecurityThreat, 'id'>): Promise<SecurityThreat>;
  updateThreat(id: string, updates: Partial<SecurityThreat>): Promise<void>;
  
  // Countermeasures
  getCountermeasures(): Promise<Countermeasure[]>;
  getCountermeasureById(id: string): Promise<Countermeasure | null>;
  activateCountermeasure(id: string): Promise<void>;
  deactivateCountermeasure(id: string): Promise<void>;
  
  // Security metrics
  getSecurityMetrics(): Promise<SecurityMetrics>;
  updateSecurityMetrics(metrics: Partial<SecurityMetrics>): Promise<void>;
  
  // Alerts
  getAlerts(): Promise<SecurityAlert[]>;
  createAlert(alert: Omit<SecurityAlert, 'id'>): Promise<SecurityAlert>;
  acknowledgeAlert(id: string): Promise<void>;
  
  // Assessments
  getLatestAssessment(): Promise<SecurityAssessment | null>;
  createAssessment(assessment: Omit<SecurityAssessment, 'id'>): Promise<SecurityAssessment>;
  
  // Events
  logSecurityEvent(event: Omit<SecurityEvent, 'id'>): Promise<void>;
  getSecurityEvents(limit?: number): Promise<SecurityEvent[]>;
}

export interface ISecurityService {
  // Threat monitoring
  scanForThreats(): Promise<SecurityThreat[]>;
  assessThreatLevel(): Promise<number>;
  getActiveThreats(): Promise<SecurityThreat[]>;
  mitigateThreat(threatId: string): Promise<boolean>;
  
  // Security metrics
  getCurrentSecurityLevel(): Promise<number>;
  getSecurityMetrics(): Promise<SecurityMetrics>;
  performSecurityAssessment(): Promise<SecurityAssessment>;
  
  // Countermeasures
  getAvailableCountermeasures(): Promise<Countermeasure[]>;
  activateCountermeasure(countermeasureId: string): Promise<boolean>;
  getActiveCountermeasures(): Promise<Countermeasure[]>;
  
  // Alerts and notifications
  getUnacknowledgedAlerts(): Promise<SecurityAlert[]>;
  acknowledgeAlert(alertId: string): Promise<void>;
  createSecurityAlert(title: string, message: string, severity: ThreatSeverity): Promise<void>;
  
  // OPSEC operations
  calculateDigitalFootprint(): Promise<number>;
  recommendSecurityMeasures(): Promise<string[]>;
  checkOperationalSecurity(): Promise<number>;
  
  // Incident response
  initiateIncidentResponse(threatId: string): Promise<void>;
  generateSecurityReport(): Promise<any>;
  
  // Monitoring
  startContinuousMonitoring(): Promise<void>;
  stopContinuousMonitoring(): Promise<void>;
}
