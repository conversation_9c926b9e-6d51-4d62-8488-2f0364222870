/**
 * API Client interfaces for backend communication
 * These will replace the mock repositories when moving to backend
 */

import {
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
  UserResponse,
  CreateUserRequest,
  UpdateUserRequest,
  MissionResponse,
  StartMissionRequest,
  MarketItemResponse,
  PurchaseItemRequest,
  SpecialistResponse,
  HireSpecialistRequest,
  SecurityThreatResponse,
  ActivateCountermeasureRequest,
  NotificationResponse,
  CreateNotificationRequest
} from './types';

// Base API client interface
export interface IApiClient {
  get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>>;
  post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>>;
  put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>>;
  patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>>;
  delete<T>(endpoint: string): Promise<ApiResponse<T>>;
  
  // Authentication
  setAuthToken(token: string): void;
  clearAuthToken(): void;
  
  // Configuration
  setBaseUrl(url: string): void;
  setTimeout(timeout: number): void;
}

// User API Client
export interface IUserApiClient {
  getCurrentUser(): Promise<ApiResponse<UserResponse>>;
  updateUser(data: UpdateUserRequest): Promise<ApiResponse<UserResponse>>;
  getUserStats(): Promise<ApiResponse<any>>;
  getUserAchievements(): Promise<ApiResponse<any[]>>;
  addExperience(amount: number): Promise<ApiResponse<void>>;
  addCredits(amount: number): Promise<ApiResponse<void>>;
  spendCredits(amount: number): Promise<ApiResponse<void>>;
  addReputation(amount: number): Promise<ApiResponse<void>>;
}

// Mission API Client
export interface IMissionApiClient {
  getMissions(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<MissionResponse>>>;
  getMissionById(id: string): Promise<ApiResponse<MissionResponse>>;
  getActiveMissions(): Promise<ApiResponse<MissionResponse[]>>;
  startMission(data: StartMissionRequest): Promise<ApiResponse<MissionResponse>>;
  completeMission(id: string): Promise<ApiResponse<MissionResponse>>;
  abandonMission(id: string): Promise<ApiResponse<void>>;
}

// Market API Client
export interface IMarketApiClient {
  getMarketItems(params?: PaginationParams & { category?: string; marketType?: string }): Promise<ApiResponse<PaginatedResponse<MarketItemResponse>>>;
  getItemById(id: string): Promise<ApiResponse<MarketItemResponse>>;
  purchaseItem(data: PurchaseItemRequest): Promise<ApiResponse<void>>;
  sellItem(itemId: string): Promise<ApiResponse<void>>;
  getInventory(): Promise<ApiResponse<MarketItemResponse[]>>;
  getMarketCategories(): Promise<ApiResponse<any[]>>;
}

// Specialist API Client
export interface ISpecialistApiClient {
  getAvailableSpecialists(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<SpecialistResponse>>>;
  getHiredSpecialists(): Promise<ApiResponse<SpecialistResponse[]>>;
  getSpecialistById(id: string): Promise<ApiResponse<SpecialistResponse>>;
  hireSpecialist(data: HireSpecialistRequest): Promise<ApiResponse<SpecialistResponse>>;
  dismissSpecialist(id: string): Promise<ApiResponse<void>>;
  startTraining(specialistId: string, programId: string): Promise<ApiResponse<void>>;
  getTrainingPrograms(): Promise<ApiResponse<any[]>>;
}

// Security API Client
export interface ISecurityApiClient {
  getSecurityMetrics(): Promise<ApiResponse<any>>;
  getThreats(): Promise<ApiResponse<SecurityThreatResponse[]>>;
  getThreatById(id: string): Promise<ApiResponse<SecurityThreatResponse>>;
  mitigateThreat(id: string): Promise<ApiResponse<void>>;
  getCountermeasures(): Promise<ApiResponse<any[]>>;
  activateCountermeasure(data: ActivateCountermeasureRequest): Promise<ApiResponse<void>>;
  deactivateCountermeasure(id: string): Promise<ApiResponse<void>>;
  performSecurityScan(): Promise<ApiResponse<any>>;
  getSecurityReport(): Promise<ApiResponse<any>>;
}

// Notification API Client
export interface INotificationApiClient {
  getNotifications(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<NotificationResponse>>>;
  getUnreadNotifications(): Promise<ApiResponse<NotificationResponse[]>>;
  createNotification(data: CreateNotificationRequest): Promise<ApiResponse<NotificationResponse>>;
  markAsRead(id: string): Promise<ApiResponse<void>>;
  markAllAsRead(): Promise<ApiResponse<void>>;
  dismissNotification(id: string): Promise<ApiResponse<void>>;
  deleteNotification(id: string): Promise<ApiResponse<void>>;
  getNotificationSettings(): Promise<ApiResponse<any>>;
  updateNotificationSettings(settings: any): Promise<ApiResponse<void>>;
}

// WebSocket API Client for real-time communication
export interface IWebSocketClient {
  connect(url: string, token?: string): Promise<void>;
  disconnect(): void;
  subscribe(eventType: string, handler: (data: any) => void): string;
  unsubscribe(subscriptionId: string): void;
  send(message: any): void;
  isConnected(): boolean;
  
  // Event handlers
  onConnect(handler: () => void): void;
  onDisconnect(handler: () => void): void;
  onError(handler: (error: Error) => void): void;
  onMessage(handler: (message: any) => void): void;
}

// Combined API client interface
export interface IGameApiClient {
  user: IUserApiClient;
  mission: IMissionApiClient;
  market: IMarketApiClient;
  specialist: ISpecialistApiClient;
  security: ISecurityApiClient;
  notification: INotificationApiClient;
  websocket: IWebSocketClient;
  
  // Authentication
  login(username: string, password: string): Promise<ApiResponse<any>>;
  logout(): Promise<ApiResponse<void>>;
  refreshToken(): Promise<ApiResponse<any>>;
  
  // Health check
  ping(): Promise<ApiResponse<{ status: string; timestamp: string }>>;
}
