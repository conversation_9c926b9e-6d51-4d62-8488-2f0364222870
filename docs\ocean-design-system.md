# Ocean Design System

## Overview

The Ocean Design System is a modern, glassmorphism-based UI framework that combines deep ocean colors with contemporary design patterns. It emphasizes clean aesthetics, subtle transparency effects, and intuitive user interactions.

## Design Philosophy

- **Ocean-Inspired**: Deep teal and blue-green color palette reminiscent of ocean depths
- **Glassmorphism**: Extensive use of backdrop blur and transparency for depth
- **Modern Minimalism**: Clean lines, generous spacing, and purposeful design
- **Accessibility First**: High contrast ratios and clear focus indicators
- **Performance Optimized**: Lightweight effects and smooth animations

## Color Palette

### Ocean Theme Colors
```css
--ocean-deep: rgb(0, 57, 82)        /* Primary background */
--ocean-medium: rgba(7, 110, 136, 0.918)   /* Elevated surfaces */
--ocean-light: rgba(10, 133, 163, 0.918)   /* Interactive elements */
--ocean-accent: rgba(0, 179, 164, 0.2)     /* Hover states */
```

### Text Colors
```css
--text-primary: #fafafa      /* Primary text */
--text-secondary: #d4d4d4    /* Secondary text */
--text-muted: #737373        /* Muted text */
```

### Status Colors
```css
--color-success: #10b981     /* Success states */
--color-error: #ef4444       /* Error states */
--color-warning: #f59e0b     /* Warning states */
--color-info: #0ea5e9        /* Info states */
```

## Typography

### Font Stack
- **Primary**: 'Inter' - Modern, highly readable sans-serif
- **Monospace**: 'JetBrains Mono' - For code and technical content

### Font Sizes
```css
--font-size-xs: 0.75rem     /* 12px */
--font-size-sm: 0.875rem    /* 14px */
--font-size-md: 1rem        /* 16px */
--font-size-lg: 1.125rem    /* 18px */
--font-size-xl: 1.25rem     /* 20px */
```

## Border Radius

### Rounded Corners
```css
--border-radius-sm: 8px      /* Small elements */
--border-radius-md: 16px     /* Medium elements */
--border-radius-lg: 24px     /* Large elements */
--border-radius-xl: 28px     /* Extra large (pill-shaped) */
```

## Glassmorphism Effects

### Glass Variants
- **`.glass`**: Basic glassmorphism with subtle transparency
- **`.glass-strong`**: Enhanced glassmorphism with stronger blur
- **`.glass-ocean`**: Ocean-themed glassmorphism with teal accents
- **`.glass-accent`**: Accent glassmorphism for interactive elements

### Implementation
```css
.glass-ocean {
  background: var(--bg-hover);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
}
```

## Component Patterns

### Layout Structure
```
AppShell (Ocean background)
├── TopBar (Glass elevated)
├── TabBar (Glass with ocean accents)
├── ContentArea (Glass strong)
└── BottomBar (Glass with pill-shaped launcher)
```

### Interactive States
- **Default**: Ocean colors with subtle transparency
- **Hover**: Increased opacity and gentle elevation
- **Active**: Ocean accent colors with glow effects
- **Focus**: Clear focus rings for accessibility

## Spacing System

### Consistent Spacing
```css
--space-xs: 4px
--space-sm: 8px
--space-md: 16px
--space-lg: 24px
--space-xl: 32px
```

## Animation Guidelines

### Timing Functions
```css
--transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1)
--transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1)
--transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1)
```

### Animation Principles
- **Micro-interactions**: 150ms for immediate feedback
- **Component transitions**: 300ms for smooth state changes
- **Page transitions**: 500ms for complex animations

## Component Examples

### Buttons
- Primary: Ocean accent background with glassmorphism
- Secondary: Transparent with ocean borders
- Hover: Gentle elevation and color shifts

### Cards
- Background: Glass-ocean effect
- Hover: Subtle elevation and accent borders
- Interactive: Smooth transform animations

### Navigation
- TabBar: Glass with ocean accent indicators
- BottomBar: Pill-shaped launcher with glass effects
- TopBar: Elevated glass with ocean-themed widgets

## Implementation Notes

### CSS Variables
All colors and effects use CSS custom properties for consistency and easy theming.

### Backdrop Filter Support
Ensure fallbacks for browsers that don't support backdrop-filter.

### Performance
- Use transform for animations instead of changing layout properties
- Limit backdrop-filter usage to essential elements
- Optimize glassmorphism effects for mobile devices

## Accessibility

### Contrast Ratios
- Text on ocean backgrounds: Minimum 4.5:1 ratio
- Interactive elements: Clear visual feedback
- Focus indicators: High contrast ocean accent colors

### Keyboard Navigation
- Clear focus rings using ocean accent colors
- Logical tab order through glassmorphism elements
- Consistent interaction patterns

## Browser Support

### Modern Features
- Backdrop-filter: Chrome 76+, Firefox 103+, Safari 9+
- CSS Grid: All modern browsers
- CSS Custom Properties: All modern browsers

### Fallbacks
- Solid backgrounds for backdrop-filter
- Standard box-shadows for unsupported effects
- Progressive enhancement approach
